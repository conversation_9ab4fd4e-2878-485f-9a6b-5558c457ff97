/**
 * @module Context Setup
 * Bootstrap script to ensure proper context setup at application startup
 */

const debug = require('debug')('multitenant:setup')
const { Context } = require('@perkd/multitenant-context')
const { LRUCache } = require('lru-cache')

// Use trap mode during startup to allow safe fallbacks
process.env.CONTEXT_MODE = 'trap'

// Print startup banner for context mode
debug('===== CONTEXT MODE: TRAP (STARTUP) =====')
debug('Trap mode enabled during startup')
debug('Will switch to strict mode after bootstrap completes')

// Context is now established in server.js before any other imports
debug('Context setup module loaded - context should already be established')

/**
 * Switch to strict mode after bootstrap completes
 */
function enableStrictMode() {
	process.env.CONTEXT_MODE = 'strict'
	debug('===== CONTEXT MODE: STRICT =====')
	debug('Strict mode enabled to prevent context mixing in parallel requests')
	debug('Hybrid/domain mode is completely disabled')
}

/**
 * Add memory debugging endpoints for Context metrics and anonymous models
 * @param {Object} app - Express/LoopBack application instance
 */
function addMemoryDebuggingEndpoints(app) {
	// Context metrics and memory debugging endpoint
	app.get('/debug/context-metrics', (req, res) => {
		try {
			const currentContext = Context.getCurrentContext()
			const contextMetrics = getContextMetrics()
			const memoryUsage = process.memoryUsage()
			const anonymousModels = getAnonymousModelStats(app)
			const tokenCacheStats = getTokenCacheStats()

			res.json({
				timestamp: new Date().toISOString(),
				contextMode: process.env.CONTEXT_MODE,
				currentContext: {
					exists: !!currentContext,
					tenant: Context.tenant || null,
					user: Context.user || null,
					timezone: Context.timezone || null,
					accessToken: Context.accessToken ? '[REDACTED]' : null
				},
				contextMetrics,
				memoryUsage: {
					...memoryUsage,
					heapUsedMB: Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100,
					heapTotalMB: Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100,
					externalMB: Math.round(memoryUsage.external / 1024 / 1024 * 100) / 100,
					rssMB: Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100
				},
				anonymousModels,
				tokenCacheStats,
				registryStats: getRegistryStats(app),
				uptime: process.uptime()
			})
		}
		catch (error) {
			res.status(500).json({
				error: 'Failed to get context metrics',
				message: error.message,
				timestamp: new Date().toISOString()
			})
		}
	})

	// Detailed context debugging endpoint
	app.get('/debug/context-detailed', (req, res) => {
		try {
			const currentContext = Context.getCurrentContext()
			const connectionManagerStats = getConnectionManagerStats(app)
			const datasourceStats = getDatasourceStats(app)

			res.json({
				timestamp: new Date().toISOString(),
				context: {
					current: currentContext ? sanitizeContext(currentContext) : null,
					mode: process.env.CONTEXT_MODE,
					tenant: Context.tenant,
					user: Context.user,
					timezone: Context.timezone
				},
				connectionManager: connectionManagerStats,
				datasources: datasourceStats,
				memoryDetails: {
					process: process.memoryUsage(),
					gc: getGCStats(),
					eventLoop: getEventLoopStats()
				}
			})
		}
		catch (error) {
			res.status(500).json({
				error: 'Failed to get detailed context info',
				message: error.message,
				timestamp: new Date().toISOString()
			})
		}
	})

	// Memory cleanup endpoint (for testing)
	app.post('/debug/context-cleanup', (req, res) => {
		try {
			const beforeMemory = process.memoryUsage()

			// Force garbage collection if available
			if (global.gc) {
				global.gc()
			}

			// Clear token cache if accessible
			clearTokenCacheIfPossible()

			const afterMemory = process.memoryUsage()

			res.json({
				timestamp: new Date().toISOString(),
				message: 'Memory cleanup attempted',
				memoryBefore: beforeMemory,
				memoryAfter: afterMemory,
				heapFreed: beforeMemory.heapUsed - afterMemory.heapUsed,
				gcAvailable: !!global.gc
			})
		}
		catch (error) {
			res.status(500).json({
				error: 'Failed to perform cleanup',
				message: error.message,
				timestamp: new Date().toISOString()
			})
		}
	})
}

/**
 * Helper functions for memory debugging
 */

/**
 * Get Context metrics if available
 */
function getContextMetrics() {
	try {
		const currentContext = Context.getCurrentContext()

		// Try to access context metrics if they exist
		const metrics = currentContext?.metrics || {}

		return {
			available: !!currentContext,
			metricsExists: !!currentContext?.metrics,
			connectionCount: metrics.connectionCount || 0,
			requestCount: metrics.requestCount || 0,
			errorCount: metrics.errorCount || 0,
			lastActivity: metrics.lastActivity || null,
			contextProperties: currentContext ? Object.keys(currentContext).filter(key => !key.startsWith('_')) : []
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Get ModelRegistry statistics
 */
function getRegistryStats(app) {
	try {
		// Access the ModelRegistry.getStats() method
		if (app.registry && typeof app.registry.getStats === 'function') {
			return {
				available: true,
				stats: app.registry.getStats()
			}
		}
		return {
			available: false,
			error: 'ModelRegistry.getStats() method not available'
		}

	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Get anonymous model statistics
 */
function getAnonymousModelStats(app) {
	try {
		const models = app.registry.modelBuilder.models || {}
		const modelNames = Object.keys(models)
		const anonymousModels = []
		const modelStats = {
			total: modelNames.length,
			anonymous: 0,
			persistent: 0,
		}

		modelNames.forEach(name => {
			const model = models[name]
			if (model) {
				// Check if model is anonymous (generated dynamically)
				const isAnonymous = !model.name || model.name.match('AnonymousModel')
				const datasourceName = model.dataSource?.name || 'unknown'

				if (isAnonymous) {
					modelStats.anonymous++
					anonymousModels.push({
						name,
						datasource: datasourceName,
						base: model.definition?.base || 'unknown'
					})
				}

				modelStats.anonymous = anonymousModels.length
				modelStats.persistent = modelStats.total - modelStats.anonymous
			}
		})

		return {
			...modelStats,
			anonymousModels: anonymousModels.slice(0, 20), // Limit to first 20 for readability
			totalAnonymousModels: anonymousModels.length
		}
	}
	catch (error) {
		return {
			error: error.message,
			total: 0,
			anonymous: 0
		}
	}
}

/**
 * Get token cache statistics from multitenant middleware
 */
function getTokenCacheStats() {
	try {
		// Import the cache stats function from multitenant middleware
		const multitenantMiddleware = require('./middleware/multitenant')
		if (typeof multitenantMiddleware.getTokenCacheStats === 'function') {
			return {
				available: true,
				...multitenantMiddleware.getTokenCacheStats()
			}
		}
		return {
			available: false,
			note: 'Token cache stats function not available',
			suggestion: 'Check multitenant middleware implementation'
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message,
			note: 'Failed to access token cache statistics'
		}
	}
}

/**
 * Get connection manager statistics
 */
function getConnectionManagerStats(app) {
	try {
		const connectionManager = app.connectionManager
		if (!connectionManager) {
			return { available: false, reason: 'No connection manager found' }
		}

		return {
			available: true,
			isInitialized: connectionManager.isInitialized || false,
			hasGetAllConnections: typeof connectionManager.getAllConnections === 'function',
			hasGetMetrics: typeof connectionManager.getMetrics === 'function',
			methods: Object.getOwnPropertyNames(Object.getPrototypeOf(connectionManager))
				.filter(name => typeof connectionManager[name] === 'function')
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Get datasource statistics
 */
function getDatasourceStats(app) {
	try {
		const datasources = app.datasources || {}
		const stats = {}

		Object.keys(datasources).forEach(name => {
			const ds = datasources[name]
			stats[name] = {
				connector: ds.connector?.name || 'unknown',
				connected: ds.connected || false,
				connecting: ds.connecting || false,
				settings: {
					host: ds.settings?.host || 'N/A',
					port: ds.settings?.port || 'N/A',
					database: ds.settings?.database || ds.settings?.url ? '[URL]' : 'N/A'
				}
			}
		})

		return {
			available: true,
			count: Object.keys(stats).length,
			datasources: stats
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Sanitize context object for safe JSON serialization
 */
function sanitizeContext(context) {
	try {
		const sanitized = {}
		for (const key in context) {
			if (key.startsWith('_') || typeof context[key] === 'function') {
				continue // Skip private properties and functions
			}
			if (key === 'domain' || key === '_domain') {
				sanitized[key] = '[Domain Object]'
			}
			else if (typeof context[key] === 'object' && context[key] !== null) {
				sanitized[key] = '[Object]'
			}
			else {
				sanitized[key] = context[key]
			}
		}
		return sanitized
	}
	catch (error) {
		return { error: 'Failed to sanitize context', message: error.message }
	}
}

/**
 * Get garbage collection statistics
 */
function getGCStats() {
	try {
		if (typeof global.gc === 'function') {
			return {
				available: true,
				note: 'GC function is available (--expose-gc flag used)'
			}
		}
		return {
			available: false,
			note: 'GC function not available (use --expose-gc flag to enable)'
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Get event loop statistics
 */
function getEventLoopStats() {
	try {
		// Basic event loop information
		return {
			uptime: process.uptime(),
			activeHandles: process._getActiveHandles ? process._getActiveHandles().length : 'N/A',
			activeRequests: process._getActiveRequests ? process._getActiveRequests().length : 'N/A',
			note: 'Limited event loop stats available without additional modules'
		}
	}
	catch (error) {
		return {
			error: error.message,
			available: false
		}
	}
}

/**
 * Clear token cache if possible
 */
function clearTokenCacheIfPossible() {
	// Token cache is module-scoped, so we can't directly access it
	// This is a placeholder for potential future implementation
	return {
		attempted: true,
		success: false,
		note: 'Token cache is module-scoped and cannot be directly cleared'
	}
}

// Export a setup function for application startup
module.exports = function setupContext(app) {
	// Log the context mode at startup
	app.on('started', () => {
		debug('Application started with STRICT context mode')
	})

	// Add a health check route to verify context mode
	app.get('/health/context-mode', (req, res) => {
		res.json({
			contextMode: process.env.CONTEXT_MODE,
			isStrict: process.env.CONTEXT_MODE === 'strict',
			time: new Date().toISOString()
		})
	})

	// Add memory debugging endpoints for Context metrics and anonymous models
	addMemoryDebuggingEndpoints(app)

	return app
}

// Export the strict mode enabler
module.exports.enableStrictMode = enableStrictMode