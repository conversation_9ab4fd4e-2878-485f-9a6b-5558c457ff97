/**
 *  @module Model:Numbering - dependent on 'findOneAndUpdate' mixin
 */
const { Dates } = require('@perkd/utils'),
	{ Settings } = require('@crm/types')

const { sameDay } = Dates,
	{ LOCALE } = Settings.Name,
	THRESHOLD = 0.1		// 10% of numbers remaining

module.exports = function(Numbering) {

	Numbering.prototype.nextNumber = async function() {
		try {
			const { id, lastAt, reset } = this,
				{ app } = Numbering,
				{ timeZone } = app.getSettings(LOCALE),
				NOW = new Date()

			if (reset?.daily && !sameDay(lastAt || NOW, NOW, timeZone)) {
				const { firstNumber = 1 } = reset

				await Numbering.findOneAndUpdate(
					{ id, lastAt },
					{ $set: { last: firstNumber - 1, lastAt: NOW } }
				)
			}

			const numbering = await Numbering.findOneAndUpdate(
					{ id },
					{ $inc: { last: 1 }, $set: { lastAt: NOW } }
				),
				{ last: number, runoutThreshold, lastNumber, prefix, suffix, length } = numbering || {}

			// Error
			if (number > lastNumber) {
				appNotify(`[Numbering]nextNumber - exceeded last number (${lastNumber})`, { numbering }, 'error')
			}

			// Warning
			if ((number + runoutThreshold) > lastNumber) {
				appNotify(`[Numbering]nextNumber - runoutThreshold (${runoutThreshold - 1} left) reached`, { numbering }, 'warn')
			}

			return [ prefix, String(number).padStart(length, '0'), suffix ].join('')
		}
		catch (err) {
			appNotify('nextNumber', { err })
			return Promise.reject(err)
		}
	}

	// -----  Remote & Operation hooks  -----

	Numbering.observe('before save', async ({ instance, data, isNewInstance }) => {
		const updated = instance || data,
			{ lastNumber, length, runoutThreshold } = updated

		if (!lastNumber) {
			if (length) updated.lastNumber = parseInt(Array(length).fill(9).join(''))	// default
		}
		if (isNewInstance && !runoutThreshold) {
			updated.runoutThreshold = Math.floor(updated.lastNumber * THRESHOLD)
		}
	})
}
