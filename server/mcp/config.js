/**
 * MCP Configuration for Business Service
 *
 * This configuration defines the MCP tools and resources for the Business service
 * using the modern factory-based architecture (MCP Core v3.1.0).
 *
 * The configuration follows the pattern established by the Image service and
 * provides comprehensive coverage of all Business service models.
 */

const { z } = require('zod')

/**
 * Business Service MCP Configuration
 *
 * Generates approximately 42 tools across 6 models:
 * - Business: 7 tools (6 CRUD + 1 specialized)
 * - Staff: 8 tools (6 CRUD + 1 specialized + 1 custom)
 * - Provider: 7 tools (6 CRUD + 1 specialized)
 * - Assignment: 6 tools (6 CRUD)
 * - StaffActivity: 6 tools (6 CRUD)
 * - Provision: 8 tools (6 CRUD + 1 specialized + 1 custom)
 */
const ServiceConfig = {
	serviceConfig: {
		serviceName: 'Business',
		defaultLimit: 20,
		maxLimit: 100,
		customOperations: {
			/**
       * Activate operation for Staff and other models
       * Sets the active status to true for the specified entity
       */
			activate: async (modelType, toolPrefix, factory) => {
				factory.extension.server.registerTool(`${toolPrefix}_activate`, {
					title: `Activate ${modelType}`,
					description: `Activate a ${modelType} by setting its active status to true`,
					inputSchema: {
						[`${modelType.toLowerCase()}Id`]: z.string().min(1).describe(`${modelType} ID to activate`)
					}
				}, async args => factory.extension.withTenantContext(async () => {
					const { SharedErrorHandler, SharedResponseFormatter, SharedModelHelper } = require('@perkd/mcp-core')

					try {
						// Validate input
						const idField = `${modelType.toLowerCase()}Id`
						const id = args[idField]

						if (!id || typeof id !== 'string' || id.trim().length === 0) {
							return SharedErrorHandler.validationError(idField, id, 'must be a non-empty string')
						}

						// Get model and find instance
						const Model = SharedModelHelper.getModel(factory.extension.app, modelType)
						const instance = await Model.findById(id)

						if (!instance) {
							return SharedErrorHandler.notFound(modelType, id)
						}

						// Update active status
						await instance.updateAttribute('active', true)

						return SharedResponseFormatter.statusChange(
							id, 'activate', true, modelType
						)
					}
					catch (error) {
						return SharedErrorHandler.handle(error, {
							operation: 'activate',
							modelType,
							id: args[`${modelType.toLowerCase()}Id`]
						}, factory.extension)
					}
				}))
			},

			/**
       * Clone operation for creating copies of entities
       * Creates a duplicate of an existing entity with optional name override
       */
			clone: async (modelType, toolPrefix, factory) => {
				factory.extension.server.registerTool(`${toolPrefix}_clone`, {
					title: `Clone ${modelType}`,
					description: `Create a copy of an existing ${modelType}`,
					inputSchema: {
						sourceId: z.string().min(1).describe(`ID of ${modelType} to clone`),
						newName: z.string().optional().describe('Name for the cloned item (optional)')
					}
				}, async args => factory.extension.withTenantContext(async () => {
					const { SharedErrorHandler, SharedResponseFormatter, SharedModelHelper } = require('@perkd/mcp-core')

					try {
						// Validate input
						if (!args.sourceId || typeof args.sourceId !== 'string' || args.sourceId.trim().length === 0) {
							return SharedErrorHandler.validationError('sourceId', args.sourceId, 'must be a non-empty string')
						}

						// Get model and find source instance
						const Model = SharedModelHelper.getModel(factory.extension.app, modelType)
						const source = await Model.findById(args.sourceId)

						if (!source) {
							return SharedErrorHandler.notFound(modelType, args.sourceId)
						}

						// Create clone data
						const sourceData = source.toJSON()
						const cloneData = { ...sourceData }

						// Remove fields that shouldn't be cloned
						delete cloneData.id
						delete cloneData.createdAt
						delete cloneData.updatedAt

						// Override name if provided
						if (args.newName) {
							cloneData.name = args.newName
						}
						else if (cloneData.name) {
							cloneData.name = `${cloneData.name} (Copy)`
						}

						// Create the clone
						const clone = await Model.create(cloneData)

						return SharedResponseFormatter.success(clone.toJSON(), toolPrefix.replace('_clone', ''))
					}
					catch (error) {
						return SharedErrorHandler.handle(error, {
							operation: 'clone',
							modelType,
							sourceId: args.sourceId
						}, factory.extension)
					}
				}))
			}
		}
	},

	modelConfigs: {
		/**
     * Business Model Configuration
     * Core business entity with comprehensive business management capabilities
     */
		Business: {
			toolPrefix: 'business',
			responseKey: 'business',
			baseSchema: {
				name: z.string().min(1).max(255).describe('Business name'),
				type: z.string().optional().describe('Business category/type'),
				brand: z.object({
					long: z.string().max(32).optional(),
					short: z.string().max(20).optional()
				}).optional().describe('Brand information'),
				isMain: z.boolean().default(false).describe('Whether this is the main business'),
				merchantCode: z.string().max(24).optional().describe('Merchant code for multi-merchant setups')
			},
			typeSpecificSchema: {
				style: z.object({}).optional().describe('Font & color palettes (light & dark themes)'),
				locale: z.object({}).optional().describe('Localization settings'),
				urls: z.array(z.object({})).optional().describe('Business URLs'),
				tags: z.object({
					system: z.array(z.string()).default([]),
					user: z.array(z.string()).default([])
				}).optional().describe('System and user tags')
			},
			specializedTools: [ {
				name: 'findByType',
				title: 'Find Businesses by Type',
				description: 'Find businesses filtered by their type/category',
				inputSchema: {
					type: z.string().describe('Business type to filter by'),
					limit: z.number().min(1).max(100).optional().default(20).describe('Maximum number of results'),
					skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
				},
				filter: args => ({ type: args.type }),
				order: 'name ASC'
			} ]
		},

		/**
     * Staff Model Configuration
     * Staff management with role-based access and credential handling
     */
		Staff: {
			toolPrefix: 'staff',
			responseKey: 'staff',
			enabledOperations: [ 'activate' ],
			baseSchema: {
				name: z.string().min(1).max(255).describe('Staff member name'),
				roles: z.array(z.string()).default([]).describe('Staff roles'),
				active: z.boolean().default(true).describe('Whether staff member is active'),
				businessId: z.string().describe('ID of the business this staff belongs to')
			},
			typeSpecificSchema: {
				identityList: z.array(z.object({
					provider: z.string(),
					externalId: z.string(),
					type: z.string().optional()
				})).optional().describe('External identity mappings'),
				dateList: z.array(z.object({
					date: z.string(),
					name: z.string()
				})).optional().describe('Important dates for staff member')
			},
			specializedTools: [ {
				name: 'findByRole',
				title: 'Find Staff by Role',
				description: 'Find staff members with specific roles',
				inputSchema: {
					role: z.string().describe('Role to filter by'),
					businessId: z.string().optional().describe('Business ID to filter by'),
					active: z.boolean().optional().describe('Filter by active status'),
					limit: z.number().min(1).max(100).optional().default(20).describe('Maximum number of results'),
					skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
				},
				filter: args => {
					const filter = { roles: args.role }
					if (args.businessId) filter.businessId = args.businessId
					if (args.active !== undefined) filter.active = args.active
					return filter
				},
				order: 'name ASC'
			} ]
		},

		/**
     * Provider Model Configuration
     * External service provider management with credentials and service configurations
     */
		Provider: {
			toolPrefix: 'provider',
			responseKey: 'provider',
			baseSchema: {
				name: z.string().min(1).max(32).describe('Provider name'),
				services: z.array(z.enum([
					'push', 'sms', 'email', 'voice', 'whatsapp', 'rich',
					'offer', 'notify', 'order', 'fulfillment', 'product', 'customer', 'discount'
				])).default([]).describe('Services provided by this provider'),
				enabled: z.boolean().default(false).describe('Whether provider is enabled'),
				liveMode: z.boolean().default(false).describe('Whether service is in production mode')
			},
			typeSpecificSchema: {
				credentials: z.object({}).optional().describe('Provider credentials (inherited from SharedProvider if exists)'),
				modules: z.array(z.object({
					name: z.string().max(32),
					enabled: z.boolean().default(false),
					options: z.object({}).default({})
				})).default([]).describe('Provider modules configuration'),
				shop: z.string().max(128).optional().describe('Shop-specific provider for multi-merchant'),
				options: z.object({}).default({}).describe('Provider-specific custom data'),
				demo: z.boolean().optional().describe('Enable test credentials in production environment')
			},
			specializedTools: [ {
				name: 'findByService',
				title: 'Find Providers by Service',
				description: 'Find providers that support a specific service',
				inputSchema: {
					service: z.enum([
						'push', 'sms', 'email', 'voice', 'whatsapp', 'rich',
						'offer', 'notify', 'order', 'fulfillment', 'product', 'customer', 'discount'
					]).describe('Service type to filter by'),
					enabled: z.boolean().optional().describe('Filter by enabled status'),
					liveMode: z.boolean().optional().describe('Filter by live mode status'),
					limit: z.number().min(1).max(100).optional().default(20).describe('Maximum number of results'),
					skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
				},
				filter: args => {
					const filter = { services: args.service }
					if (args.enabled !== undefined) filter.enabled = args.enabled
					if (args.liveMode !== undefined) filter.liveMode = args.liveMode
					return filter
				},
				order: 'name ASC'
			} ]
		},

		/**
     * Assignment Model Configuration
     * Staff-place assignment tracking with time periods
     */
		Assignment: {
			toolPrefix: 'assignment',
			responseKey: 'assignment',
			baseSchema: {
				staffId: z.string().describe('ID of the assigned staff member'),
				placeId: z.string().describe('ID of the assigned place'),
				visible: z.boolean().default(true).describe('Whether assignment is visible')
			},
			typeSpecificSchema: {
				start: z.string().optional().describe('Assignment start date (ISO string)'),
				end: z.string().optional().describe('Assignment end date (ISO string)')
			}
		},

		/**
     * StaffActivity Model Configuration
     * Activity tracking for staff members (extends base Activity model)
     */
		StaffActivity: {
			toolPrefix: 'staffactivity',
			responseKey: 'activity',
			baseSchema: {
				ownerId: z.string().describe('ID of the staff member who owns this activity'),
				type: z.string().describe('Type of activity'),
				description: z.string().optional().describe('Activity description')
			},
			typeSpecificSchema: {
				metadata: z.object({}).optional().describe('Additional activity metadata'),
				timestamp: z.string().optional().describe('Activity timestamp (ISO string)')
			}
		},

		/**
     * Provision Model Configuration
     * Business provisioning and setup operations
     */
		Provision: {
			toolPrefix: 'provision',
			responseKey: 'provision',
			enabledOperations: [ 'clone' ],
			baseSchema: {
				name: z.string().min(1).describe('Provision name'),
				status: z.enum([ 'pending', 'in-progress', 'completed', 'failed' ]).default('pending').describe('Provision status'),
				type: z.string().describe('Type of provision operation')
			},
			typeSpecificSchema: {
				businessId: z.string().optional().describe('Associated business ID'),
				configuration: z.object({}).optional().describe('Provision configuration data'),
				metadata: z.object({}).optional().describe('Additional provision metadata'),
				startTime: z.string().optional().describe('Provision start time (ISO string)'),
				endTime: z.string().optional().describe('Provision end time (ISO string)')
			},
			specializedTools: [ {
				name: 'findByStatus',
				title: 'Find Provisions by Status',
				description: 'Find provision operations filtered by their status',
				inputSchema: {
					status: z.enum([ 'pending', 'in-progress', 'completed', 'failed' ]).describe('Status to filter by'),
					type: z.string().optional().describe('Provision type to filter by'),
					businessId: z.string().optional().describe('Business ID to filter by'),
					limit: z.number().min(1).max(100).optional().default(20).describe('Maximum number of results'),
					skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
				},
				filter: args => {
					const filter = { status: args.status }
					if (args.type) filter.type = args.type
					if (args.businessId) filter.businessId = args.businessId
					return filter
				},
				order: 'createdAt DESC'
			} ]
		}
	}
}

module.exports = { ServiceConfig }
