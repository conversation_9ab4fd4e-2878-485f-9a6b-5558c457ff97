/**
 * MCP Extension for Business Service
 *
 * This extension implements the Model Context Protocol (MCP) for the Business service
 * using the modern factory-based architecture (MCP Core v3.1.0).
 *
 * The extension provides comprehensive access to Business service models through
 * auto-generated tools, specialized business operations, and comprehensive documentation.
 *
 * Generated Tools:
 * - Business: 7 tools (6 CRUD + 1 specialized)
 * - Staff: 8 tools (6 CRUD + 1 specialized + 1 custom)
 * - Provider: 7 tools (6 CRUD + 1 specialized)
 * - Assignment: 6 tools (6 CRUD)
 * - StaffActivity: 6 tools (6 CRUD)
 * - Provision: 8 tools (6 CRUD + 1 specialized + 1 custom)
 *
 * Total: 42 tools across 6 models
 */

const { BaseExtension, SharedDocumentationProvider } = require('@perkd/mcp-core')
const { ServiceConfig } = require('./config')

/**
 * Business Service MCP Extension
 *
 * IMPORTANT: This class MUST be named 'McpExtension' and the file MUST be named 'mcp-extension.js'
 * The CRM MCP module is hardcoded to load this class from this file.
 */
class McpExtension extends BaseExtension {
	/**
   * Initialize the MCP extension
   *
   * This method sets up the factory-based architecture, registers all tools,
   * and configures documentation resources and interactive prompts.
   */
	async initialize() {
		try {
			// 1. Initialize shared factory libraries
			await this.initializeFactories(ServiceConfig, {
				generateSchema: true,
				generateExamples: true,
				generateUploadDocs: false // Business service doesn't handle file uploads
			})

			// 2. Auto-register all factory tools
			await this.registerAllFactoryTools()

			// 3. Add documentation resources
			await this.registerDocumentationResources()

			// 4. Add interactive prompts
			await this.registerServicePrompts()

			// 5. Register business-specific custom tools
			await this.registerBusinessSpecificTools()

			console.log(`📊 Total tools available: ${this.getToolCount()}`)

		}
		catch (error) {
			console.error('❌ Failed to initialize Business Service MCP Extension:', error)
			throw error
		}
	}

	/**
   * Register documentation resources for all models
   *
   * Creates comprehensive documentation including schemas, examples,
   * and service overview information.
   */
	async registerDocumentationResources() {
		try {
			// Register documentation for each model
			for (const modelType of Object.keys(ServiceConfig.modelConfigs)) {
				const modelConfig = ServiceConfig.modelConfigs[modelType]
				const toolPrefix = modelConfig.toolPrefix

				// Schema documentation
				this.server.registerResource(
					`${toolPrefix}-schema`,
					`${toolPrefix}://schema`,
					{
						title: `${modelType} Schema`,
						mimeType: 'application/json',
						description: `Complete schema definition for ${modelType} model`
					},
					async () => {
						const Model = this.app.models[modelType]
						if (!Model) {
							throw new Error(`Model ${modelType} not found`)
						}
						const documentation = SharedDocumentationProvider.generateCompleteDocumentation(Model, modelConfig)
						return {
							contents: [ {
								uri: `${toolPrefix}://schema`,
								mimeType: 'application/json',
								text: JSON.stringify(documentation, null, 2)
							} ]
						}
					}
				)

				// Usage examples
				this.server.registerResource(
					`${toolPrefix}-examples`,
					`${toolPrefix}://examples`,
					{
						title: `${modelType} Examples`,
						mimeType: 'application/json',
						description: `Usage examples for ${modelType} tools`
					},
					async () => {
						const Model = this.app.models[modelType]
						if (!Model) {
							throw new Error(`Model ${modelType} not found`)
						}
						const docs = SharedDocumentationProvider.generateCompleteDocumentation(Model, modelConfig)
						const examples = docs.examples || {}
						return {
							contents: [ {
								uri: `${toolPrefix}://examples`,
								mimeType: 'application/json',
								text: JSON.stringify(examples, null, 2)
							} ]
						}
					}
				)
			}

			// Service overview
			this.server.registerResource(
				'service-overview',
				'service://overview',
				{
					title: 'Business Service Overview',
					mimeType: 'application/json',
					description: 'Complete overview of Business service capabilities'
				},
				() => {
					const overview = this.generateServiceOverview()
					return {
						contents: [ {
							uri: 'service://overview',
							mimeType: 'application/json',
							text: JSON.stringify(overview, null, 2)
						} ]
					}
				}
			)

			// Model types reference
			this.server.registerResource(
				'service-types',
				'service://types',
				{
					title: 'Business Service Model Types',
					mimeType: 'application/json',
					description: 'Reference guide for all Business service model types'
				},
				() => {
					const types = this.generateModelTypesReference()
					return {
						contents: [ {
							uri: 'service://types',
							mimeType: 'application/json',
							text: JSON.stringify(types, null, 2)
						} ]
					}
				}
			)

		}
		catch (error) {
			console.error('Failed to register documentation resources:', error)
			throw error
		}
	}

	/**
   * Register interactive service prompts
   *
   * Creates interactive guides for using the Business service tools.
   */
	async registerServicePrompts() {
		try {
			// Business management guide
			this.server.registerPrompt('business-management-guide', {
				title: 'Business Management Guide',
				description: 'Interactive guide for managing businesses, staff, and providers'
			}, async () => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: 'How do I manage businesses and staff in the CRM system?'
						}
					},
					{
						role: 'assistant',
						content: {
							type: 'text',
							text: this.generateBusinessManagementGuide()
						}
					}
				]
			}))

			// Provider configuration guide
			this.server.registerPrompt('provider-configuration-guide', {
				title: 'Provider Configuration Guide',
				description: 'Guide for configuring external service providers'
			}, async () => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: 'How do I configure and manage external service providers?'
						}
					},
					{
						role: 'assistant',
						content: {
							type: 'text',
							text: this.generateProviderConfigurationGuide()
						}
					}
				]
			}))

		}
		catch (error) {
			console.error('Failed to register service prompts:', error)
			throw error
		}
	}

	/**
   * Register business-specific custom tools
   *
   * Implements domain-specific operations that go beyond standard CRUD.
   */
	async registerBusinessSpecificTools() {
		try {
			// Business provisioning workflow tool
			this.server.registerTool('business_provision', {
				title: 'Provision New Business',
				description: 'Complete business provisioning workflow including business creation, staff setup, and provider configuration',
				inputSchema: {
					businessData: {
						name: { type: 'string', description: 'Business name' },
						type: { type: 'string', description: 'Business type/category' },
						merchantCode: { type: 'string', description: 'Merchant code (optional)' }
					},
					ownerData: {
						name: { type: 'string', description: 'Owner name' },
						email: { type: 'string', description: 'Owner email' },
						mobile: { type: 'object', description: 'Owner mobile number' }
					},
					configuration: { type: 'object', description: 'Additional configuration options' }
				}
			}, async args => this.withTenantContext(async () => {
				const { SharedErrorHandler, SharedResponseFormatter } = require('@perkd/mcp-core')

				try {
					// Validate required fields
					if (!args.businessData?.name) {
						return SharedErrorHandler.validationError('businessData.name', args.businessData?.name, 'is required')
					}

					if (!args.ownerData?.name) {
						return SharedErrorHandler.validationError('ownerData.name', args.ownerData?.name, 'is required')
					}

					// Use the existing Provision.createBusiness method
					const { Provision } = this.app.models
					const business = await Provision.createBusiness({
						name: args.businessData.name,
						type: args.businessData.type,
						merchantCode: args.businessData.merchantCode,
						owner: args.ownerData,
						profile: args.configuration || {}
					})

					return SharedResponseFormatter.success({
						business: business.toJSON(),
						message: 'Business provisioned successfully',
						nextSteps: [
							'Configure payment providers',
							'Set up staff roles and permissions',
							'Configure business settings and branding'
						]
					}, 'provision')

				}
				catch (error) {
					return SharedErrorHandler.handle(error, {
						operation: 'provision',
						businessName: args.businessData?.name
					}, this)
				}
			}))

			// Staff credential refresh tool
			this.server.registerTool('staff_refresh_credential', {
				title: 'Refresh Staff Credentials',
				description: 'Refresh authentication credentials for a staff member',
				inputSchema: {
					staffId: { type: 'string', description: 'Staff member ID' }
				}
			}, async args => this.withTenantContext(async () => {
				const { SharedErrorHandler, SharedResponseFormatter } = require('@perkd/mcp-core')

				try {
					if (!args.staffId) {
						return SharedErrorHandler.validationError('staffId', args.staffId, 'is required')
					}

					const { Staff } = this.app.models
					const staff = await Staff.findById(args.staffId)

					if (!staff) {
						return SharedErrorHandler.notFound('Staff', args.staffId)
					}

					// Use the existing refreshCredential method
					await staff.refreshCredential()

					return SharedResponseFormatter.success({
						staffId: args.staffId,
						message: 'Staff credentials refreshed successfully',
						timestamp: new Date().toISOString()
					}, 'credential')

				}
				catch (error) {
					return SharedErrorHandler.handle(error, {
						operation: 'refresh_credential',
						staffId: args.staffId
					}, this)
				}
			}))

		}
		catch (error) {
			console.error('Failed to register business-specific tools:', error)
			throw error
		}
	}

	/**
   * Generate service overview information
   */
	generateServiceOverview() {
		const modelCount = Object.keys(ServiceConfig.modelConfigs).length
		const toolCount = this.getToolCount()

		return {
			service: 'Business',
			version: '1.0.0',
			description: 'Comprehensive business management service for the CRM platform',
			models: modelCount,
			tools: toolCount,
			capabilities: [
				'Business profile management',
				'Staff management with role-based access',
				'External provider configuration',
				'Staff-place assignment tracking',
				'Activity monitoring and audit trails',
				'Business provisioning workflows'
			],
			modelTypes: Object.keys(ServiceConfig.modelConfigs),
			lastUpdated: new Date().toISOString()
		}
	}

	/**
   * Generate model types reference
   */
	generateModelTypesReference() {
		const types = {}

		for (const [ modelType, config ] of Object.entries(ServiceConfig.modelConfigs)) {
			types[modelType] = {
				toolPrefix: config.toolPrefix,
				responseKey: config.responseKey,
				description: this.getModelDescription(modelType),
				toolCount: this.getModelToolCount(modelType, config),
				hasSpecializedTools: (config.specializedTools || []).length > 0,
				hasCustomOperations: (config.enabledOperations || []).length > 0
			}
		}

		return types
	}

	/**
   * Get description for a model type
   */
	getModelDescription(modelType) {
		const descriptions = {
			Business: 'Core business entity with profile, branding, and configuration management',
			Staff: 'Staff member management with roles, credentials, and activity tracking',
			Provider: 'External service provider configuration and credential management',
			Assignment: 'Staff-place assignment tracking with time periods',
			StaffActivity: 'Activity tracking and audit trails for staff members',
			Provision: 'Business provisioning and setup operation management'
		}

		return descriptions[modelType] || `${modelType} model`
	}

	/**
   * Get tool count for a specific model
   */
	getModelToolCount(modelType, config) {
		let count = 6 // Base CRUD + query + count
		count += (config.specializedTools || []).length
		count += (config.enabledOperations || []).length
		return count
	}

	/**
   * Get total tool count
   */
	getToolCount() {
		let total = 0
		for (const [ modelType, config ] of Object.entries(ServiceConfig.modelConfigs)) {
			total += this.getModelToolCount(modelType, config)
		}
		total += 2 // business_provision + staff_refresh_credential
		return total
	}

	/**
   * Generate business management guide
   */
	generateBusinessManagementGuide() {
		return `# Business Management Guide

## Available Tools

### Business Management (7 tools)
- \`business_create\` - Create new business
- \`business_get\` - Get business by ID
- \`business_list\` - List businesses with pagination
- \`business_delete\` - Delete business
- \`business_query\` - Advanced business queries
- \`business_count\` - Count businesses
- \`business_findByType\` - Find businesses by type

### Staff Management (8 tools)
- \`staff_create\` - Create new staff member
- \`staff_get\` - Get staff by ID
- \`staff_list\` - List staff with pagination
- \`staff_delete\` - Delete staff member
- \`staff_query\` - Advanced staff queries
- \`staff_count\` - Count staff members
- \`staff_findByRole\` - Find staff by role
- \`staff_activate\` - Activate staff member

### Provider Management (7 tools)
- \`provider_create\` - Create new provider
- \`provider_get\` - Get provider by ID
- \`provider_list\` - List providers
- \`provider_delete\` - Delete provider
- \`provider_query\` - Advanced provider queries
- \`provider_count\` - Count providers
- \`provider_findByService\` - Find providers by service

### Special Operations
- \`business_provision\` - Complete business provisioning workflow
- \`staff_refresh_credential\` - Refresh staff credentials

## Common Workflows

1. **Create New Business**: Use \`business_provision\` for complete setup
2. **Manage Staff**: Use \`staff_create\` then \`staff_findByRole\` to organize
3. **Configure Providers**: Use \`provider_create\` then \`provider_findByService\`

For detailed examples, see the model-specific documentation resources.`
	}

	/**
   * Generate provider configuration guide
   */
	generateProviderConfigurationGuide() {
		return `# Provider Configuration Guide

## Supported Services
- push, sms, email, voice, whatsapp, rich
- offer, notify, order, fulfillment
- product, customer, discount

## Configuration Steps

1. **Create Provider**: Use \`provider_create\` with service types
2. **Configure Credentials**: Set up authentication in credentials object
3. **Enable Services**: Set enabled: true and configure modules
4. **Test Configuration**: Use demo mode for testing

## Example Workflow

1. \`provider_create\` - Create provider with basic info
2. \`provider_findByService\` - Find existing providers for reference
3. Update credentials and enable services
4. Test with demo mode before going live

Use \`provider_get\` to verify configuration and \`provider_list\` to see all configured providers.`
	}
}

module.exports = { McpExtension }
