{"name": "Business", "plural": "Businesses", "description": "CRM", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {"validateUpsert": true}, "mixins": {"Timestamp": true, "Multitenant": true, "Mongo": true, "Errors": true, "Common": true, "Queue": true, "Date": true, "Phone": true, "Email": true, "Address": true, "Identity": true, "Tag": true, "DoUpsert": true, "Event": {"hook": {"create": true, "update": true, "delete": true}, "doUpserOnly": true}, "Image": {"model": "LogoImage", "relation": "logos", "description": "Upload logo image of Business"}, "Setting": true, "MainSetting": true, "Access": true, "PayPrimitives": true, "Pay": true, "Provider": true, "PayApi": true, "StoredValueApi": true, "PaymentApi": true, "DisableAllRemotes": {"create": true, "upsert": true, "updateAll": true, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "findOrCreate": true, "deleteById": true, "confirm": true, "count": true, "exists": true, "replaceById": true, "replaceOrCreate": true, "upsertWithWhere": true, "prototype.__create__logos": true, "prototype.__get__dashboards": true, "prototype.__get__settings": true, "prototype.__create__dashboards": true}}, "properties": {"type": {"type": "String", "description": "Business category"}, "name": {"type": "String"}, "brand": {"type": {"long": {"type": "string", "max": 32}, "short": {"type": "string", "max": 20}}, "default": {}}, "style": {"type": "Style", "default": {}, "description": "Font & color palettes (light & dark themes) in hex"}, "locale": {"type": "Locale", "default": {}}, "urls": {"type": ["Url"]}, "tags": {"type": "Tag", "default": {"system": [], "user": []}}, "notes": {"type": ["Note"], "default": []}, "isMain": {"type": "Boolean", "default": false}, "merchantCode": {"type": "string", "max": 24, "description": "used by MultiMerchant"}, "callbacks": {"type": [{"type": "Callback"}]}, "place": {"type": {"category": {"type": "string"}}, "default": {}}, "socials": {"type": [{"type": "string", "max": 16, "enum": ["facebook", "instagram", "line", "whatsapp", "wechat", "weibo"], "description": "Name of social platform, matching 'provider' in 'identities' stores details"}]}, "wifi": {"type": {"enabled": {"type": "boolean", "default": false}, "ssid": {"type": "string", "max": 31}}}, "facebook": {"type": {"pixelId": {"type": "string"}}}, "google": {"type": {"measurementId": {"type": "string"}}}, "payments": {"type": {"payee": {"type": "string", "description": "Formal name of payee"}, "limits": {"min": {"type": "number", "required": true, "description": "Min total amount per payment"}, "max": {"type": "number", "required": true, "description": "Max total amount per payment"}}, "fees": {"type": {"rate": {"type": "number", "description": "Fees included in transaction amount, in decimal percentage. eg. 0.1 => 10%"}, "options": {"type": "object", "description": "{ includeFulfillment: true, repeatPurchase: true }"}}}}}, "commerce": {"type": {"taxIncluded": {"type": "boolean", "default": true, "description": "Tax included in subtotalPrice"}, "taxes": {"type": [{"type": {"title": {"type": "string", "description": "Name of tax"}, "rate": {"type": "number", "description": "Tax rate in fraction"}}}]}}}, "storedValue": {"type": {}}, "offer": {"type": {"purge": {"type": "object", "default": {"duration": "P90D", "endOf": "day"}, "description": "The default rule which is used to generate purgeTime, based on Offer endTime"}}, "default": {}}, "reward": {"type": {"purge": {"type": "object", "default": {"duration": "P365D", "endOf": "day"}, "description": "The default rule which is used to generate purgeTime, based on Reward endTime"}}, "default": {}}, "message": {"type": {"purge": {"type": "object", "default": {"duration": "P90D", "endOf": "day"}, "description": "The default rule which is used to generate purgeTime, based on Perkd Message send time"}}, "default": {}}, "credentials": {"type": {"stripe": {"type": {"publishableKey": {"type": "string"}}}, "mypay": {"type": {"gatewayMerchantId": {"type": "string"}}}}, "default": {}, "description": "{ <provider>: <secret> } pairs for storing external credentials, used by App through CardMaster"}, "external": {"type": {"perkd": {"type": {"storeId": {"type": "String"}}}, "shopify": {"type": {"storeId": {"type": "String"}}}, "google": {"type": {"geoId": {"type": "String"}}, "description": "Google place id"}, "pos": {"type": {"storeId": {"type": "String"}}}}, "default": {}, "description": "Reference to source of truth"}, "visible": {"type": "Boolean", "default": true}, "globalize": {"type": "Globalize", "default": {}}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}, "deletedAt": {"type": "Date"}}, "validations": [], "relations": {"dates": {"type": "embeds<PERSON><PERSON>", "model": "Date", "property": "dateList", "options": {"validate": false, "unique": ["date", "name"], "persistent": true, "forceId": false}}, "phones": {"type": "embeds<PERSON><PERSON>", "model": "Phone", "property": "phoneList", "options": {"validate": false, "unique": "fullNumber", "persistent": true, "prepend": true, "forceId": false}}, "emails": {"type": "embeds<PERSON><PERSON>", "model": "Email", "property": "emailList", "options": {"validate": false, "unique": "address", "persistent": true, "prepend": true, "forceId": false}}, "addresses": {"type": "embeds<PERSON><PERSON>", "model": "Address", "property": "addressList", "options": {"validate": false, "persistent": true, "prepend": true, "forceId": false}}, "identities": {"type": "embeds<PERSON><PERSON>", "model": "Identity", "property": "identityList", "options": {"validate": false, "forceId": false, "persistent": true}}, "settings": {"type": "embeds<PERSON><PERSON>", "model": "Setting", "property": "settingList", "options": {"validate": true, "forceId": false}}, "logos": {"type": "hasMany", "model": "LogoImage", "foreignKey": "ownerId"}, "gateway": {"type": "hasOne", "model": "Gateway", "foreignKey": "ownerId"}, "places": {"type": "hasMany", "model": "Place", "foreignKey": "ownerId"}, "user": {"type": "belongsTo", "model": "User", "foreignKey": "userId"}, "owner": {"type": "belongsTo", "model": "Person", "foreignKey": "ownerId"}, "staff": {"type": "hasMany", "model": "Staff", "foreignKey": "businessId"}, "dashboards": {"type": "hasMany", "model": "Dashboard", "foreignKey": "businessId"}, "candidatePlaces": {"type": "hasMany", "model": "Place", "foreignKey": "ownerId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"getMain": {"description": "Get main Business", "http": {"path": "/main", "verb": "get"}, "accepts": [], "returns": {"type": "Business", "root": true}}, "getStores": {"description": "Get all the operating stores", "http": {"path": "/getStores", "verb": "get"}, "accepts": [{"arg": "from", "type": "string"}, {"arg": "to", "type": "string"}], "returns": {"type": "array", "root": true}}, "getStaff": {"description": "get Staff of given Role(s) during the (optional) Period", "http": {"path": "/getStaff", "verb": "get"}, "accepts": [{"arg": "roles", "type": "array"}, {"arg": "from", "type": "string"}, {"arg": "to", "type": "string"}], "returns": {"type": "array", "root": true}}, "getAssignment": {"description": "get Assignements (to stores) of given Staff(s) during the (optional) Period", "http": {"path": "/getAssignment", "verb": "get"}, "accepts": [{"arg": "staffList", "type": "array", "required": true}, {"arg": "from", "type": "string"}, {"arg": "to", "type": "string"}], "returns": {"type": "array", "root": true}}, "createWallet": {"description": "Create a wallet (Payment API)", "http": {"path": "/payment/wallet", "verb": "post"}, "accepts": [{"arg": "id", "type": "string", "required": true}, {"arg": "type", "type": "string", "required": true, "description": "Type of wallet/payment, eg. storedvalue"}, {"arg": "membershipId", "type": "string", "required": true, "description": "Membership to link wallet"}, {"arg": "profile", "type": "object", "description": "{ name, email, mobile }, maybe required by Payment Provider for account creation"}], "returns": {"type": "Payment", "root": true}}, "getWallet": {"description": "Get (merchant) wallet (Payment API)", "http": {"path": "/payment/wallet", "verb": "get"}, "accepts": [{"arg": "id", "type": "string", "required": true}, {"arg": "type", "type": "string", "required": true, "description": "Type of wallet/payment, eg. storedvalue"}], "returns": {"type": "Payment", "root": true}}, "findIdentity": {"description": "Find an Identity for provider", "http": {"path": "/identity", "verb": "get"}, "accepts": [{"arg": "id", "type": "any", "required": true}, {"arg": "provider", "type": "string", "required": true}, {"arg": "type", "type": "string"}, {"arg": "domain", "type": "string"}], "returns": {"type": "string", "root": true}}, "prototype.getDashboardUrl": {"description": "Get an embeddable quicksight dashboard URL", "http": {"path": "/dashboards/:fk/url", "verb": "get"}, "accepts": [{"arg": "fk", "type": "string", "http": {"source": "path"}}], "returns": {"type": "Object", "root": true}}}}