# The manifest for the "business" service.
# Read the full specification for the "Load Balanced Web Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: business
type: Load Balanced Web Service

# Distribute traffic to your service.
http:
  # Requests to this path will be forwarded to your service.
  # To match all requests you can use the "/" path.
  path: '/'
  # You can specify a custom health check path. The default is "/".
  healthcheck:
    path: '/health'
    success_codes: '200'
    healthy_threshold: 2    # number of consecutive health check successes required before considering an unhealthy target healthy
    unhealthy_threshold: 4  # number of consecutive health check failures required before considering a target unhealthy
    interval: 15s           # amount of time, in seconds, between health checks
    timeout: 10s            # amount of time, in seconds, during which no response from a target means a failed health check
    grace_period: 5s        # grace period within which to provide containers time to bootstrap before failed health checks count towards the maximum number of retries
  deregistration_delay: 10s  # amount of time to wait for targets to drain connections during deregistration.

# Expose MCP endpoints
  additional_rules:
    - path: '/mcp*'
      target_port: 8081  # Port where MCP endpoint is running
      healthcheck:
        path: '/mcp/health'
        success_codes: '200'

# Configuration for your containers and service.
image:
  # Docker build arguments. For additional overrides: https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/#image-build
  build: 
    dockerfile: Dockerfile
  # Port exposed through your container to route traffic to it.
  port: 3120
   # Addtional port mapping for MCP
  additional_ports:
    - 8081

cpu: 256       # Number of CPU units for the task.
memory: 512    # Amount of memory in MiB used by the task.
count: 1       # Number of tasks that should be running in your service.
platform: linux/arm64
exec: true     # Enable running commands in your container.

network:
  vpc:
    placement: 'private'

# Optional fields for more advanced use-cases.
#
variables:                    # Pass environment variables as key value pairs.
  ENV_REGION: 'ap-southeast-1'

#secrets:                      # Pass secrets from AWS Systems Manager (SSM) Parameter Store.
#  GITHUB_TOKEN: GITHUB_TOKEN  # The key is the name of the environment variable, the value is the name of the SSM parameter.

# You can override any of the values defined above by environment.
environments:
  test:
    # count:
    #   range: 1-3
    #   memory_percentage: 80
    variables:
      NODE_ENV: 'test'
      REDIS_HOST: 'redis.test.crm.local'
      REDIS_CLOUD_HOST: 'redis-18368.c1.ap-southeast-1-1.ec2.cloud.redislabs.com'
      REDIS_CLOUD_PORT: 18368
      PROVIDER_REDIS_HOST: 'redis-18368.c1.ap-southeast-1-1.ec2.cloud.redislabs.com'
      PROVIDER_REDIS_PORT: 18368
      METRICS_HOST: 'mission-control.test.crm.local'
      METRICS_PORT: 8125
      PROMETHEUS_HOST: 'mission-control.test.crm.local'
      PROMETHEUS_PORT: 9090
      ACTION_HOST: 'action.perkd.me/test'
      PLACE_HOST: 'place.test.crm.local'
      PERSON_HOST: 'person.test.crm.local'
      MEMBERSHIP_HOST: 'membership.test.crm.local'
      IMAGE_HOST: 'image.test.crm.local'
      PAYMENT_HOST: 'payment.test.crm.local'
      ACCOUNT_HOST: 'account.test.crm.local'
      SALES_HOST: 'sales.test.crm.local'
      PRODUCT_HOST: 'product.test.crm.local'
      CAMPAIGN_HOST: 'campaign.test.crm.local'
      OFFER_HOST: 'offer.test.crm.local'
    secrets:
      DB_HOST: /copilot/crm/test/secrets/DB_HOST
      PERKD_SECRET_KEY: /copilot/crm/test/secrets/PERKD_SECRET_KEY
      ACTION_SECRET: /copilot/crm/test/secrets/ACTION_SECRET
      WATCHDOG_KEY_ID: /copilot/crm/test/secrets/WATCHDOG_KEY_ID
      WATCHDOG_SECRET: /copilot/crm/test/secrets/WATCHDOG_SECRET
      DB_USERNAME: /copilot/crm/test/secrets/DB_USERNAME
      DB_PASSWORD: /copilot/crm/test/secrets/DB_PASSWORD
      REDIS_USERNAME: /copilot/crm/test/secrets/REDIS_USERNAME
      REDIS_PASSWORD: /copilot/crm/test/secrets/REDIS_PASSWORD
      REDIS_CLOUD_USERNAME: /copilot/crm/test/secrets/REDIS_CLOUD_USERNAME
      REDIS_CLOUD_PASSWORD: /copilot/crm/test/secrets/REDIS_CLOUD_PASSWORD
      PROVIDER_REDIS_USERNAME: /copilot/crm/test/secrets/REDIS_CLOUD_USERNAME
      PROVIDER_REDIS_PASSWORD: /copilot/crm/test/secrets/REDIS_CLOUD_PASSWORD
      PROMETHEUS_USERNAME: /copilot/crm/test/secrets/PROMETHEUS_USERNAME
      PROMETHEUS_PASSWORD: /copilot/crm/test/secrets/PROMETHEUS_PASSWORD
  production:
    cpu: 256
    memory: 512
    count:
      range: 1-3
      memory_percentage: 80
    variables:
      NODE_ENV: 'production'
      REDIS_HOST: 'redis.production.crm.local'
      REDIS_CLOUD_HOST: 'redis-18366.c1.ap-southeast-1-1.ec2.cloud.redislabs.com'
      REDIS_CLOUD_PORT: 18366
      PROVIDER_REDIS_HOST: 'redis-18366.c1.ap-southeast-1-1.ec2.cloud.redislabs.com'
      PROVIDER_REDIS_PORT: 18366
      METRICS_HOST: 'mission-control.production.crm.local'
      METRICS_PORT: 8125
      PROMETHEUS_HOST: 'mission-control.production.crm.local'
      PROMETHEUS_PORT: 9090
      ACTION_HOST: 'action.perkd.me/production'
      PLACE_HOST: 'place.production.crm.local'
      PERSON_HOST: 'person.production.crm.local'
      MEMBERSHIP_HOST: 'membership.production.crm.local'
      IMAGE_HOST: 'image.production.crm.local'
      PAYMENT_HOST: 'payment.production.crm.local'
      ACCOUNT_HOST: 'account.production.crm.local'
      SALES_HOST: 'sales.production.crm.local'
      PRODUCT_HOST: 'product.production.crm.local'
      CAMPAIGN_HOST: 'campaign.production.crm.local'
      OFFER_HOST: 'offer.production.crm.local'
    secrets:
      PERKD_SECRET_KEY: /copilot/crm/production/secrets/PERKD_SECRET_KEY
      ACTION_SECRET: /copilot/crm/production/secrets/ACTION_SECRET
      WATCHDOG_KEY_ID: /copilot/crm/production/secrets/WATCHDOG_KEY_ID
      WATCHDOG_SECRET: /copilot/crm/production/secrets/WATCHDOG_SECRET
      DB_USERNAME: /copilot/crm/production/secrets/DB_USERNAME
      DB_PASSWORD: /copilot/crm/production/secrets/DB_PASSWORD
      DB_HOST: /copilot/crm/production/secrets/DB_HOST
      DB_SET: /copilot/crm/production/secrets/DB_SET
      REDIS_USERNAME: /copilot/crm/production/secrets/REDIS_USERNAME
      REDIS_PASSWORD: /copilot/crm/production/secrets/REDIS_PASSWORD
      REDIS_CLOUD_USERNAME: /copilot/crm/production/secrets/REDIS_CLOUD_USERNAME
      REDIS_CLOUD_PASSWORD: /copilot/crm/production/secrets/REDIS_CLOUD_PASSWORD
      PROVIDER_REDIS_USERNAME: /copilot/crm/production/secrets/REDIS_CLOUD_USERNAME
      PROVIDER_REDIS_PASSWORD: /copilot/crm/production/secrets/REDIS_CLOUD_PASSWORD
      PROMETHEUS_USERNAME: /copilot/crm/production/secrets/PROMETHEUS_USERNAME
      PROMETHEUS_PASSWORD: /copilot/crm/production/secrets/PROMETHEUS_PASSWORD