# Business Payment System Documentation

## Table of Contents
- [Overview](#overview)
- [Architecture](#architecture)
- [Payment Mixins](#payment-mixins)
  - [PayPrimitives](#payprimitives)
  - [Pay](#pay)
  - [Comparison of Pay and Buy Mixins](#comparison-of-pay-and-buy-mixins)
  - [PayApi](#payapi)
  - [PaymentApi](#paymentapi)
  - [StoredValueApi](#storedvalueapi)
  - [Payment](#payment)
- [Multi-Merchant Support](#multi-merchant-support)
  - [Store Configuration](#store-configuration)
  - [Merchant Resolution](#merchant-resolution)
- [Payment Flows](#payment-flows)
  - [Standard Payment Flow](#standard-payment-flow)
  - [Stored Value Flow](#stored-value-flow)
  - [Payment Authorization and Capture](#payment-authorization-and-capture)
  - [Asynchronous Wallet Payments Flow](#asynchronous-wallet-payments-flow)
- [API Endpoints](#api-endpoints)
  - [Business Payment Endpoints](#business-payment-endpoints)
  - [Stored Value Endpoints](#stored-value-endpoints)
  - [Callback API Endpoints](#callback-api-endpoints)
- [Payment Fees and Fee Management](#payment-fees-and-fee-management)
  - [Fee Structure Overview](#fee-structure-overview)
  - [Fee Configuration](#fee-configuration)
  - [Fee Calculation Logic](#fee-calculation-logic)
  - [Fee Processing Workflow](#fee-processing-workflow)
  - [Fee Policy Examples](#fee-policy-examples)
  - [Fee Management Best Practices](#fee-management-best-practices)
- [Payment Settings](#payment-settings)
  - [Business Payment Settings](#business-payment-settings)
  - [Store Payment Settings](#store-payment-settings)
- [Implementation Examples](#implementation-examples)
  - [Making a Payment](#making-a-payment)
  - [Stored Value Operations](#stored-value-operations)
  - [Multi-Merchant Payment](#multi-merchant-payment)


## Overview

The Business Payment System provides a comprehensive solution for processing payments across multiple payment methods, supporting multi-merchant configurations, and handling stored value operations. The system is designed with a layered architecture that separates core payment operations from business logic and API endpoints.

Key features include:
- Support for multiple payment methods (credit card, Apple Pay, Google Pay, Alipay, etc.)
- Multi-merchant configuration for businesses with multiple stores
- Stored value operations (gift cards, store credits)
- Payment authorization and capture
- Refund processing
- Balance management

## Architecture

The payment system follows a layered architecture:

1. **Base Layer (PayPrimitives)**: Provides fundamental payment operations that interact directly with payment providers.
2. **Business Logic Layer (Pay, Payment)**: Builds on the base layer to provide business logic for payment processing, including order-related payment operations.
3. **API Layer (PayApi, PaymentApi, StoredValueApi)**: Exposes payment functionality as remote API endpoints for external systems.

```mermaid
graph TD
    subgraph "API Layer"
        PayApi[PayApi Mixin]
        PaymentApi[PaymentApi Mixin]
        StoredValueApi[StoredValueApi Mixin]
    end

    subgraph "Business Logic Layer"
        Pay[Pay Mixin]
        Payment[Payment Mixin]
    end

    subgraph "Base Layer"
        PayPrimitives[PayPrimitives Mixin]
    end

    PayApi --> Pay
    PaymentApi --> Pay
    StoredValueApi --> PayPrimitives
    Pay --> PayPrimitives
    Payment --> PayPrimitives
```

## Payment Mixins

### PayPrimitives

**Purpose**: Provides the fundamental payment operations (Level-0 primitives) that other payment mixins build upon.

**Key Features**:
- Defines core payment operations
- Manages wallet operations for different payment methods
- Handles multi-merchant scenarios
- Provides error handling for payment operations

**Key Methods**:
- `paymentRequest()`: Creates a payment intent with a payment provider
- `paymentAuthorize()`: Authorizes a payment without capturing funds
- `paymentCapture()`: Captures previously authorized funds
- `paymentCancel()`: Cancels a payment intent
- `paymentRefund()`: Processes refunds for completed payments
- Stored value operations: `storedvalueInit()`, `storedvalueTopup()`, `storedvalueDeduct()`, `storedvalueTransfer()`

### Pay

**Purpose**: Builds on PayPrimitives to provide higher-level payment functionality, including event handling and order-related payment operations.

**Key Features**:
- Depends on PayPrimitives mixin
- Handles payment events
- Provides endpoints used by the Payment mixin remotely
- Implements methods for Cardmaster callback APIs

**Key Methods**:
- `handlePaymentEvent()`: Processes payment events from payment providers
- `pay()`: Processes payments for orders, handling multiple payment methods
- `payCommit()`: Commits pending payments, updating order status
- `payCancel()`: Cancels payments, updating order status

### Comparison of Pay and Buy Mixins

The system provides two primary methods for processing payments: `.pay()` in the Pay mixin and `.orderPay()` in the Buy mixin. Understanding the differences between these methods is crucial, especially when handling asynchronous wallet payments like Alipay, LINEPay, and GrabPay.

| Feature | `.pay()` (Pay Mixin) | `.orderPay()` (Buy Mixin) |
|---------|----------------------|----------------------------|
| **Primary Purpose** | Process payments for existing orders | Create orders with payments in one operation |
| **Order Creation** | Creates order after payment processing | Creates order regardless of payment status |
| **Async Payment Handling** | Limited explicit handling for 'pending' state | Explicit branch for pending/async payments |
| **Qualification Process** | No qualification process | Includes qualification and benefit application |
| **Fulfillment** | No direct fulfillment | Includes fulfillment for paid orders |
| **Error Recovery** | Creates order for server errors with authorized payments | Creates order for server errors with authorized parts |
| **Event Emission** | Emits events based on payment status | Relies on other methods for event emission |

### PayApi

**Purpose**: Exposes payment functionality as remote API endpoints for external systems (like Cardmaster).

**Key Features**:
- Provides remote API endpoints for payment operations
- Acts as a bridge between external systems and the Pay mixin

**Key Methods**:
- `pay()`: Remote endpoint for making payments
- `payCommit()`: Remote endpoint for committing pending payments
- `paymentMethodAdd()`: Remote endpoint for adding payment methods

### PaymentApi

**Purpose**: Provides external API endpoints specifically for vending machines and other external systems.

**Key Features**:
- Focused on payment operations for vending machines
- Handles payment authorization and capture for external systems

**Key Methods**:
- `paymentApiCommit()`: Captures authorized payments for vending machines
- `paymentApiCancel()`: Cancels authorized payments for vending machines

### StoredValueApi

**Purpose**: Provides API endpoints for stored value (gift cards, store credits) operations.

**Key Features**:
- Handles stored value transfers between cards
- Provides balance checking for stored value cards
- Exposes these operations as remote API endpoints

**Key Methods**:
- `storedvalueSend()`: Transfers value from one card to another
- `storedvalueBalance()`: Retrieves the balance of a stored value card

### Payment

**Purpose**: Implements payment handlers for models that use the Buy mixin, providing a bridge between order processing and payment processing.

**Key Features**:
- Must be used together with the Buy mixin
- Implements payment handlers required by the Buy mixin
- Delegates payment operations to the Business model

**Key Methods**:
- `paymentRequest()`: Requests a payment for an order
- `paymentAuthorize()`: Authorizes a payment for an order
- `paymentCapture()`: Captures a previously authorized payment
- `paymentCancel()`: Cancels a payment
- `paymentRefund()`: Processes a refund

## Multi-Merchant Support

The payment system supports multi-merchant configurations, allowing different stores to have their own payment settings and merchant accounts.

### Store Configuration

This section describes the mechanism for retrieving store-specific payment settings. For actual data structure of these settings, see [Store Payment Settings](#store-payment-settings).

The `getStorePaymentSetting()` function is a key component of the multi-merchant support, retrieving payment settings for a specific store:

```javascript
/**
 * Get payment setting required by payment provider, eg. merchantId of sub-merchants
 * @param {String} storeId
 * @return {Object|void} payment setting
 */
Business.getStorePaymentSetting = async function(storeId) {
    if (!storeId) return

    const { Place } = Business.app.models,
        place = await Place.findById(storeId),
        settings = place?.getSettingsByName(PAYMENT)

    return settings
}
```

This function:
1. Takes a store ID as input
2. Retrieves the store (Place) from the database
3. Gets the payment settings for the store using the `getSettingsByName(PAYMENT)` method
4. Returns the payment settings, which typically include `merchantId` and other provider-specific settings

### Merchant Resolution

The `merchantOf()` function determines which merchant (Business) should be used for a given store:

```javascript
Business.merchantOf = async function(storeId) {
    const { app } = Business,
        { Place } = app.models,
        { multiMerchant } = app.getSettings(PAYMENT)

    if (multiMerchant === true && storeId) {
        const store = await Place.findById(storeId),
            { ownerId } = store ?? {}

        if (!ownerId) return this.getMain()

        const business = await Business.findById(ownerId),
            { payments } = business ?? {}

        return payments ? business : this.getMain()  // fallback to main if no payments configured
    }

    return this.getMain()
}
```

This function:
1. Checks if multi-merchant mode is enabled in the payment settings
2. If enabled and a store ID is provided, retrieves the store and its owner
3. Returns the owner business if it has payment settings configured
4. Falls back to the main business if no owner is found or the owner doesn't have payment settings

## Payment Flows

### Standard Payment Flow

A standard payment flow involves the following steps:

1. **Payment Request**: The client initiates a payment request with payment details.
2. **Payment Authorization**: The system authorizes the payment with the payment provider.
3. **Payment Capture**: The system captures the authorized payment.
4. **Order Update**: The system updates the order status based on the payment result.

```mermaid
sequenceDiagram
    participant Client
    participant PayApi
    participant Pay
    participant PayPrimitives
    participant Provider

    Client->>PayApi: pay(userId, payments, order)
    PayApi->>Pay: pay(userId, payments, order)
    Pay->>PayPrimitives: paymentRequest(method, request, details, options)
    PayPrimitives->>Provider: createIntent(method, req, det, opt)
    Provider-->>PayPrimitives: transaction
    PayPrimitives-->>Pay: transaction
    Pay-->>PayApi: response
    PayApi-->>Client: { payment, fulfilled }
```

### Stored Value Flow

Stored value operations involve the following flows:

1. **Initialization**: Creating a stored value wallet with an initial amount.
2. **Top-up**: Adding funds to an existing wallet.
3. **Deduction**: Deducting funds from a wallet for a purchase.
4. **Transfer**: Transferring funds between wallets.

```mermaid
sequenceDiagram
    participant Client
    participant StoredValueApi
    participant PayPrimitives
    participant Provider

    Client->>StoredValueApi: storedvalueInit(walletId, amount)
    StoredValueApi->>PayPrimitives: storedvalueInit(walletId, amount)
    PayPrimitives->>Provider: createPayout(amount, currency, accountId, details)
    Provider-->>PayPrimitives: transaction
    PayPrimitives-->>StoredValueApi: { balance, currency }
    StoredValueApi-->>Client: { balance, currency }
```

### Payment Authorization and Capture

For payment methods that support authorization and capture:

1. **Authorization**: The system authorizes the payment without capturing funds.
2. **Capture**: The system captures the authorized funds at a later time.

```mermaid
sequenceDiagram
    participant Client
    participant PayApi
    participant Pay
    participant PayPrimitives
    participant Provider

    Client->>PayApi: pay(userId, payments, order, { capture: false })
    PayApi->>Pay: pay(userId, payments, order, { capture: false })
    Pay->>PayPrimitives: paymentAuthorize(method, request, details, options)
    PayPrimitives->>Provider: createIntent(method, req, det, { capture: false })
    Provider-->>PayPrimitives: transaction (status: AUTHORIZED)
    PayPrimitives-->>Pay: transaction
    Pay-->>PayApi: response
    PayApi-->>Client: { payment, pendingId }

    Note over Client,Provider: Later...

    Client->>PaymentApi: paymentApiCommit(intentId, order)
    PaymentApi->>Pay: payCommit(transaction)
    Pay->>PayPrimitives: paymentCapture(transaction)
    PayPrimitives->>Provider: captureIntent(intentId, amount)
    Provider-->>PayPrimitives: transaction (status: PAID)
    PayPrimitives-->>Pay: transaction
    Pay-->>PaymentApi: { amount, referenceId }
    PaymentApi-->>Client: { amount, referenceId }
```

### Asynchronous Wallet Payments Flow

For asynchronous wallet payment methods like LINEPay, Alipay, and GrabPay that have a `pending` payment state:

1. **Payment Initiation**: The system creates a payment request with the provider.
2. **Order Creation**: The system creates an order with a `pending` status.
3. **Redirect**: The user is redirected to the payment provider's platform to complete the payment.
4. **Callback/Webhook**: The provider sends a callback/webhook to notify of payment completion.
5. **Payment Commit**: The system processes the callback and updates the order status.

```mermaid
sequenceDiagram
    participant Client
    participant PayApi
    participant Pay
    participant PayPrimitives
    participant Provider
    participant ProviderUI

    Client->>PayApi: pay(userId, [{method: 'linepay', ...}], order)
    PayApi->>Pay: pay(userId, payments, order)
    Pay->>PayPrimitives: paymentRequest(method, request, details, options)
    PayPrimitives->>Provider: createIntent(method, req, det, opt)
    Provider-->>PayPrimitives: transaction (status: PENDING, redirectUrl)
    PayPrimitives-->>Pay: transaction

    Note over Pay: Create order with pending status

    Pay-->>PayApi: { payment: { pendingId, expiresAt, redirectUrl } }
    PayApi-->>Client: { payment: { pendingId, expiresAt, redirectUrl } }

    Client->>ProviderUI: Redirect to payment provider UI
    ProviderUI-->>Client: Complete payment on provider platform

    Note over Provider,Pay: Asynchronous callback/webhook

    Provider->>Pay: handlePaymentEvent(transaction)
    Pay->>Pay: payCommit(transaction)

    alt Payment Successful (PAID)
        Pay->>PayPrimitives: Update billing with transaction
        Pay->>Pay: Capture any pending payments
        Pay->>Pay: Mark order as paid
        Pay->>Pay: Emit payment.paid event
    else Payment Failed (FAILED)
        Pay->>PayPrimitives: paymentCancel(transaction)
        Pay->>Pay: Update billing with failed status
        Pay->>Pay: Emit payment.failed event
    else Payment Cancelled (CANCELLED)
        Pay->>Pay: Update billing with transaction
        Pay->>Pay: Cancel order
        Pay->>Pay: Emit payment.failed event
    end

    Pay-->>Provider: Acknowledge callback

    Note over Client,Pay: Client polls for status or receives push notification

    Client->>PayApi: Check payment status
    PayApi->>Pay: Get order status
    Pay-->>PayApi: Order status (PAID/FAILED/CANCELLED)
    PayApi-->>Client: Payment result
```

**Important Implementation Notes for Asynchronous Payments:**

1. **Order Creation Timing**: Orders must be created even when payment is in `pending` state to ensure proper tracking.
2. **Transaction Reference**: The payment provider's reference ID must be stored with the order for callback matching.
3. **Timeout Handling**: Implement proper timeout handling for payments that remain in `pending` state too long.
4. **Error Recovery**: For server errors during asynchronous payment processing, ensure the order is still created to prevent lost transactions.

## API Endpoints

### Business Payment Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/Businesses/payment/request` | POST | Initialize payment request |
| `/Businesses/payment/authorize` | POST | Authorize payment |
| `/Businesses/payment/capture` | POST | Capture authorized payment |
| `/Businesses/payment/cancel` | DELETE | Cancel payment request |
| `/Businesses/payment/refund` | POST | Process refund |
| `/Businesses/payment/balance` | GET | Get wallet balance |

### Stored Value Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/Businesses/payment/storedvalue/init` | POST | Initialize wallet with amount |
| `/Businesses/payment/storedvalue/topup` | POST | Top up wallet with amount |
| `/Businesses/payment/storedvalue/deduct` | POST | Deduct amount from wallet |
| `/Businesses/payment/storedvalue/transfer` | POST | Transfer amount between wallets |

### Wallet Management Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/Businesses/payment/wallet` | GET | Get merchant Wallet |
| `/Businesses/payment/wallet` | POST | Create a (consumer) wallet |

### Perkd Callback Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/Businesses/perkd/pay` | POST | Make payment to business |
| `/Businesses/perkd/pay/commit` | POST | Commit/capture authorized payment |
| `/Businesses/perkd/pay/cancel` | POST | Cancel authorized payment |
| `/Businesses/perkd/pay/send` | POST | Send amount from a card to another |
| `/Businesses/perkd/pay/balance` | GET | Retrieve balance |
| `/Businesses/perkd/pay/method` | POST | Store PaymentMethod with Provider for future use |
| `/Businesses/perkd/pay/method` | DELETE | Remove stored PaymentMethod with Provider |

### Perkd Pay API Endpoints (for vending machines)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/Businesses/pay/commit` | POST | Capture authorized payment |
| `/Businesses/pay/cancel` | POST | Cancel authorized payment |

> Note: For additional API documentation including detailed request/response formats, see [API.md](../API.md).

## Payment Fees and Fee Management

The payment system includes a comprehensive fee management framework that allows businesses to configure and apply fees to payment transactions based on various business rules and policies.

### Fee Structure Overview

Payment fees are configured at the business level through the `payments.fees` property in the Business model. The fee system supports:

- **Percentage-based fees**: Applied as a decimal rate (e.g., 0.03 = 3%)
- **Conditional fee application**: Based on fulfillment inclusion and repeat purchase behavior
- **Dynamic fee calculation**: Computed during payment processing based on order composition

### Fee Configuration

#### Basic Fee Structure

```json
{
  "payments": {
    "fees": {
      "rate": 0.025,
      "options": {
        "excludeFulfillment": false,
        "repeatPurchase": false
      }
    }
  }
}
```

#### Fee Configuration Properties

| Property | Type | Description |
|----------|------|-------------|
| `rate` | Number | Base fee rate as decimal percentage (0.025 = 2.5%) |
| `options.excludeFulfillment` | Boolean | If true, excludes fulfillment costs from fee calculation |
| `options.repeatPurchase` | Boolean | If true, fees only apply to repeat customers (lifetime purchase count > 1) |

### Fee Calculation Logic

The fee calculation follows this algorithm:

1. **Base Validation**: Check if fee rate is configured and order amount > 0
2. **Repeat Purchase Check**: If enabled, verify customer has made previous purchases
3. **Fulfillment Adjustment**: Optionally exclude fulfillment costs from fee base
4. **Effective Rate Calculation**: Compute final fee rate based on adjusted amount

#### Fee Calculation Formula

```javascript
// Basic calculation
feeAmount = adjustedPrice * baseRate

// Effective rate (applied to total price)
effectiveRate = (adjustedPrice * baseRate) / totalPrice

// Where adjustedPrice is:
adjustedPrice = excludeFulfillment ?
  (totalPrice - fulfillmentCost) :
  totalPrice
```

### Fee Processing Workflow

```mermaid
sequenceDiagram
    participant Client
    participant PayPrimitives
    participant getFees
    participant Member
    participant Provider

    Client->>PayPrimitives: paymentRequest(method, request, details, options)
    PayPrimitives->>getFees: getFees(feesPolicy, request, details)

    alt repeatPurchase enabled
        getFees->>Member: findOne({ where: { personId } })
        Member-->>getFees: member with purchase history
        getFees->>getFees: Check lifetime purchase count
    end

    getFees->>getFees: Calculate adjusted price (exclude fulfillment if configured)
    getFees->>getFees: Compute effective fee rate
    getFees-->>PayPrimitives: calculated fees

    PayPrimitives->>Provider: createIntent(method, request, details, { fees, ... })
    Provider-->>PayPrimitives: transaction with fees applied
    PayPrimitives-->>Client: transaction result
```

### Fee Policy Examples

#### Standard Fee Policy
```json
{
  "payments": {
    "fees": {
      "rate": 0.03,
      "options": {}
    }
  }
}
```
- Applies 3% fee to all transactions
- Includes fulfillment costs in fee calculation
- Applies to all customers (new and repeat)

#### Fulfillment-Excluded Fee Policy
```json
{
  "payments": {
    "fees": {
      "rate": 0.025,
      "options": {
        "excludeFulfillment": true
      }
    }
  }
}
```
- Applies 2.5% fee only to product costs
- Excludes delivery/fulfillment charges from fee calculation
- Useful for businesses that want to avoid double-charging on delivery

#### Repeat Customer Fee Policy
```json
{
  "payments": {
    "fees": {
      "rate": 0.02,
      "options": {
        "repeatPurchase": true
      }
    }
  }
}
```
- Applies 2% fee only to repeat customers
- First-time customers pay no fees
- Based on member lifetime purchase count

#### Combined Policy
```json
{
  "payments": {
    "fees": {
      "rate": 0.015,
      "options": {
        "excludeFulfillment": true,
        "repeatPurchase": true
      }
    }
  }
}
```
- Applies 1.5% fee to repeat purchases only
- Excludes fulfillment costs from fee calculation
- Combines both conditional policies

#### Integration with Payment Processing

Fees are automatically calculated and applied during payment processing:

1. **Fee Calculation**: Called during `paymentRequest()` processing
2. **Provider Integration**: Fees passed to payment provider via options
3. **Transaction Recording**: Fee information included in transaction details

### Fee Management Best Practices

1. **Rate Configuration**: Use decimal values (0.025 for 2.5%)
2. **Testing**: Thoroughly test fee calculations with different order compositions
3. **Customer Communication**: Clearly communicate fee policies to customers
4. **Monitoring**: Track fee revenue and impact on customer behavior
5. **Compliance**: Ensure fee policies comply with local regulations

## Payment Settings

### Business Payment Settings

Business payment settings are stored in the Business model and include:

- `payee`: Formal name of the payee
- `limits`: Minimum and maximum payment amounts
- `fees`: Fee structure for payments (see [Payment Fees and Fee Management](#payment-fees-and-fee-management) for detailed configuration)
- `multiMerchant`: Flag indicating if multi-merchant mode is enabled

Example:
```json
{
  "payment": {
    "payee": "Example Business",
    "limits": {
      "min": 1.00,
      "max": 10000.00
    },
    "fees": {
      "rate": 0.03,
      "options": {
        "includeFulfillment": true,
        "repeatPurchase": true
      }
    },
    "multiMerchant": true
  }
}
```

### Store Payment Settings

Store payment settings are stored in the Place model and typically include:

- `merchantId`: ID of the sub-merchant for the store
- Provider-specific settings

Example:
```json
{
  "payment": {
    "merchantId": "sub_merchant_123",
    "provider": "stripe",
    "options": {
      "connectChargeType": "direct"
    }
  }
}
```

## Implementation Examples

### Making a Payment

```javascript
// Create a payment request
const transaction = await business.paymentRequest(
  'card',                                // method
  { amount: 100, currency: 'USD' },      // request
  {
    userId: 'user123',                   // details
    intent: { paymentMethodId: 'pm_123' },
    storeId: 'store123'
  },
  { capture: true }                      // options
);

// Handle the response
if (transaction.status === 'PAID') {
  // Payment successful
  console.log(`Payment successful: ${transaction.amount} ${transaction.currency}`);
} else {
  // Payment failed
  console.error(`Payment failed: ${transaction.status}`);
}
```

### Stored Value Operations

```javascript
// Initialize a stored value wallet
const { balance, currency } = await business.storedvalueInit(
  'wallet123',    // walletId
  100,            // amount
  0.1,            // discount (10%)
  new Date('2023-12-31')  // expiresAt
);

// Top up a stored value wallet
const result = await business.storedvalueTopup(
  'card123',      // cardId
  50,             // amount
  0,              // discount
  null            // expiresAt
);

// Deduct from a stored value wallet
const deduction = await business.storedvalueDeduct(
  'card123',      // cardId
  25              // amount
);

// Transfer between wallets
const transfer = await Business.storedvalueTransfer(
  'wallet123',    // from
  'wallet456',    // to
  75              // amount
);
```

### Multi-Merchant Payment

```javascript
// Get the merchant for a store
const merchant = await Business.merchantOf('store123');

// Get store payment settings
const storeSettings = await Business.getStorePaymentSetting('store123');
const { merchantId } = storeSettings;

// Make a payment using the store's merchant ID
const transaction = await merchant.paymentRequest(
  'card',
  { amount: 100, currency: 'USD' },
  {
    userId: 'user123',
    intent: { paymentMethodId: 'pm_123' },
    storeId: 'store123'
  },
  { capture: true }
);
```
