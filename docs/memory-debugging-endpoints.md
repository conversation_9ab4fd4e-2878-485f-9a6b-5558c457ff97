# Memory Debugging Endpoints

This document describes the memory debugging endpoints added to help with memory leak debugging in the CRM Business service.

## Available Endpoints

### 1. Context Metrics Endpoint
**URL**: `GET /debug/context-metrics`

**Purpose**: Provides comprehensive memory and context statistics for debugging memory leaks.

**Response Structure**:
```json
{
  "timestamp": "2025-01-03T10:30:00.000Z",
  "contextMode": "strict",
  "currentContext": {
    "exists": true,
    "tenant": "trap",
    "user": null,
    "timezone": null,
    "accessToken": "[REDACTED]"
  },
  "contextMetrics": {
    "available": true,
    "metricsExists": false,
    "connectionCount": 0,
    "requestCount": 0,
    "errorCount": 0,
    "lastActivity": null,
    "contextProperties": ["tenant", "user", "timezone"]
  },
  "memoryUsage": {
    "rss": 123456789,
    "heapTotal": 67108864,
    "heapUsed": 45678901,
    "external": 2345678,
    "arrayBuffers": 123456,
    "heapUsedMB": 43.56,
    "heapTotalMB": 64.0,
    "externalMB": 2.24,
    "rssMB": 117.73
  },
  "anonymousModels": {
    "total": 45,
    "anonymous": 2,
    "persistent": 15,
    "transient": 20,
    "remote": 10,
    "anonymousModels": [
      {
        "name": "DynamicModel1",
        "datasource": "trap",
        "base": "PersistedModel"
      }
    ],
    "totalAnonymousModels": 2
  },
  "tokenCacheStats": {
    "available": true,
    "size": 15,
    "max": 1000,
    "ttl": 900000,
    "calculatedSize": 15,
    "disposed": 0,
    "hits": 150,
    "misses": 25,
    "hitRate": "85.71%"
  },
  "uptime": 3600.5
}
```

### 2. Detailed Context Debugging Endpoint
**URL**: `GET /debug/context-detailed`

**Purpose**: Provides detailed context information including connection manager and datasource statistics.

**Response Structure**:
```json
{
  "timestamp": "2025-01-03T10:30:00.000Z",
  "context": {
    "current": {
      "tenant": "trap",
      "user": null,
      "timezone": null
    },
    "mode": "strict",
    "tenant": "trap",
    "user": null,
    "timezone": null
  },
  "connectionManager": {
    "available": true,
    "isInitialized": true,
    "hasGetAllConnections": false,
    "hasGetMetrics": false,
    "methods": ["ensureConnection", "validateConnection", "cleanup"]
  },
  "datasources": {
    "available": true,
    "count": 8,
    "datasources": {
      "trap": {
        "connector": "mongodb",
        "connected": true,
        "connecting": false,
        "settings": {
          "host": "mongodb",
          "port": 27017,
          "database": "[URL]"
        }
      },
      "shared": {
        "connector": "mongodb",
        "connected": true,
        "connecting": false,
        "settings": {
          "host": "mongodb",
          "port": 27017,
          "database": "[URL]"
        }
      }
    }
  },
  "memoryDetails": {
    "process": {
      "rss": 123456789,
      "heapTotal": 67108864,
      "heapUsed": 45678901,
      "external": 2345678,
      "arrayBuffers": 123456
    },
    "gc": {
      "available": false,
      "note": "GC function not available (use --expose-gc flag to enable)"
    },
    "eventLoop": {
      "uptime": 3600.5,
      "activeHandles": 25,
      "activeRequests": 3,
      "note": "Limited event loop stats available without additional modules"
    }
  }
}
```

### 3. Memory Cleanup Endpoint
**URL**: `POST /debug/context-cleanup`

**Purpose**: Attempts to perform memory cleanup and garbage collection for testing purposes.

**Response Structure**:
```json
{
  "timestamp": "2025-01-03T10:30:00.000Z",
  "message": "Memory cleanup attempted",
  "memoryBefore": {
    "rss": 123456789,
    "heapTotal": 67108864,
    "heapUsed": 45678901,
    "external": 2345678,
    "arrayBuffers": 123456
  },
  "memoryAfter": {
    "rss": 120000000,
    "heapTotal": 67108864,
    "heapUsed": 42000000,
    "external": 2345678,
    "arrayBuffers": 123456
  },
  "heapFreed": 3678901,
  "gcAvailable": false
}
```

## Usage Examples

### Basic Memory Check
```bash
curl -H "tenant-code: trap" http://localhost:3120/debug/context-metrics
```

### Detailed Context Analysis
```bash
curl -H "tenant-code: trap" http://localhost:3120/debug/context-detailed
```

### Force Memory Cleanup
```bash
curl -X POST -H "tenant-code: trap" http://localhost:3120/debug/context-cleanup
```

## Key Metrics for Memory Leak Detection

### 1. **Anonymous Models**
- Monitor `anonymousModels.totalAnonymousModels` - should not grow indefinitely
- Check `anonymousModels.anonymousModels` array for unexpected dynamic models

### 2. **Token Cache**
- Monitor `tokenCacheStats.size` - should stay within reasonable bounds
- Check `tokenCacheStats.hitRate` - low hit rates may indicate cache thrashing
- Watch `tokenCacheStats.disposed` - high disposal rates may indicate memory pressure

### 3. **Memory Usage**
- Monitor `memoryUsage.heapUsedMB` - should not grow continuously
- Check `memoryUsage.rssMB` - total memory usage
- Compare before/after cleanup to see if memory is being released

### 4. **Context Metrics**
- Monitor `contextMetrics.connectionCount` - should not grow indefinitely
- Check `contextMetrics.errorCount` - high error counts may indicate issues

### 5. **Connection Manager**
- Verify `connectionManager.isInitialized` is true
- Monitor available methods for debugging capabilities

## Troubleshooting

### High Memory Usage
1. Check anonymous models count
2. Verify token cache size is reasonable
3. Use cleanup endpoint to test if memory can be freed
4. Monitor context metrics for connection leaks

### Context Issues
1. Verify `contextMode` is "strict"
2. Check `currentContext.exists` is true
3. Monitor context properties for unexpected data

### Cache Issues
1. Check token cache hit rate
2. Monitor cache size growth
3. Verify TTL settings are appropriate

## Security Notes

- These endpoints should only be available in development/staging environments
- Token information is redacted in responses
- Consider adding authentication/authorization for production debugging
