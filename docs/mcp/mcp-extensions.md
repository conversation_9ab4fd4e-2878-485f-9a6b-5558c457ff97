# MCP Extensions Development Guide

This guide shows how to implement MCP (Model Context Protocol) extensions in Loopback 3 microservices.

## Table of Contents

- [🏗️ Architecture Overview](#️-architecture-overview)
- [� Configuration Setup](#-configuration-setup)
- [�🚀 Quick Start with Factory Libraries](#-quick-start-with-factory-libraries)
- [📋 Configuration Reference](#-configuration-reference)
- [🛠️ Implementation Guide](#️-implementation-guide)
- [📚 Auto-Generated Documentation](#-auto-generated-documentation)
- [🔧 Advanced Configuration](#-advanced-configuration)
- [🔍 Troubleshooting Configuration](#-troubleshooting-configuration)

## 🏗️ Architecture Overview

Understanding the MCP Server architecture is essential before implementing extensions. Your MCP extension operates within a layered system that provides protocol handling, platform services, and business logic integration.

### **MCP Server Runtime Architecture**

```
┌────────────────────────────────────────────────────────────┐
│                    MCP Server (Port 8081)                  │
├────────────────────────────────────────────────────────────┤
│  Transport Layer: Streamable HTTP                          │
│  Protocol Version: 2025-06-18                              │
├────────────────────────────────────────────────────────────┤
│                   MCP Extension Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │  Factory Tools  │  │   Resources     │                  │
│  │  - CRUD Ops     │  │  - Schemas      │                  │
│  │  - Specialized  │  │  - Examples     │                  │
│  │  - Business     │  │  - Overview     │                  │
│  └─────────────────┘  └─────────────────┘                  │
├────────────────────────────────────────────────────────────┤
│                  Service Integration                       │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │  Data Models    │  │  Business Logic │                  │
│  │  - Offer        │  │  - Qualification│                  │
│  │  - OfferMaster  │  │  - Redemption   │                  │
│  └─────────────────┘  └─────────────────┘                  │
├────────────────────────────────────────────────────────────┤
│                    CRM Platform Base                       │
│  Multi-tenancy • Authentication • Event Bus • Metrics      │
└────────────────────────────────────────────────────────────┘
```

### **Key Architecture Concepts for Extension Developers**

**1. Extension Layer Integration**
- Your extension operates in the MCP Extension Layer
- Automatic tool generation from your model configurations
- Built-in resource documentation and examples
- Factory libraries handle common patterns (CRUD, queries, specialized operations)

**2. Platform Services Available**
- **Multi-tenancy**: Automatic tenant context isolation using `@perkd/multitenant-context`
- **Authentication**: Integrated auth handling for secure operations
- **Event Bus**: Publish/subscribe for cross-service communication
- **Metrics**: Built-in performance and usage tracking

**3. Protocol & Transport**
- MCP Protocol 2025-06-18 compliance
- Streamable HTTP transport for efficient communication
- JSON Schema validation for all tool inputs/outputs
- Automatic error handling and response formatting

**4. Development Structure**
```
server/mcp/
├── config.js           # Extension configuration
└── mcp-extension.js    # Extension implementation
```

Your extension configuration defines models and operations, while the MCP core handles protocol compliance, transport, and platform integration automatically.

## � Configuration Setup

Before implementing MCP extensions, you must configure two essential files to enable MCP functionality in your service.

### **Required Configuration Files**

1. **`server/config.json`** - Enables the MCP module in your service
2. **`server/config/mcp.json`** - Configures the MCP server settings

### **Step 1: Enable MCP Module**

Edit `server/config.json` to enable the MCP module in your service:

```json
{
  "restApiRoot": "/api",
  "host": "0.0.0.0",
  "port": 3031,
  "service": {
    "name": "YourService",
    "domain": "yourservice",
    "version": "1.0.0",
    "multitenancy": true
  },
  "modules": {
    "metrics": {
      "enabled": true
    },
    "eventbus": {
      "enabled": true
    },
    "watchdog": {
      "enabled": true
    },
    "mcp": {
      "enabled": true
    }
  }
}
```

**Key Points:**
- The `modules.mcp.enabled: true` property is **required** to activate MCP functionality
- Without this setting, the MCP module will not load regardless of other configuration
- This integrates with the service's module loading system

### **Step 2: Configure MCP Server**

Create `server/config/mcp.json` to configure the MCP server instance:

```json
{
  "enabled": true,
  "name": "crm-yourservice-mcp",
  "version": "1.0.0",
  "port": 8081,
  "path": "/mcp",
  "transport": "streamable-http",
  "protocolVersion": "2025-06-18",
  "security": {
    "allowedOrigins": ["*"],
    "requireAuthentication": true,
    "oauth": {
      "enabled": true
    }
  },
  "session": {
    "enabled": true,
    "timeout": 1800000,
    "cleanupInterval": 300000
  },
  "logging": {
    "enabled": true,
    "level": "debug"
  },
  "bodyParser": {
    "jsonLimit": "10mb"
  }
}
```

### **Configuration Properties Reference**

#### **Required Properties**
| Property | Type | Description |
|----------|------|-------------|
| `enabled` | boolean | Whether the MCP server is enabled |
| `name` | string | Unique identifier for your MCP server (format: `crm-{service}-mcp`) |
| `version` | string | Server version (typically matches service version) |
| `port` | number | Port for MCP server to listen on (typically 8081+) |

#### **Optional Properties**
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `path` | string | `"/mcp"` | URL path for MCP endpoint |
| `transport` | string | `"streamable-http"` | Transport protocol type |
| `protocolVersion` | string | `"2025-06-18"` | MCP protocol version |
| `security.allowedOrigins` | array | `["*"]` | CORS allowed origins |
| `security.requireAuthentication` | boolean | `true` | Whether to require OAuth authentication |
| `security.oauth.enabled` | boolean | `true` | Enable OAuth 2.1 authentication |
| `session.enabled` | boolean | `true` | Enable session management |
| `session.timeout` | number | `1800000` | Session timeout in milliseconds (30 min) |
| `session.cleanupInterval` | number | `300000` | Session cleanup interval in milliseconds (5 min) |
| `logging.enabled` | boolean | `true` | Enable MCP-specific logging |
| `logging.level` | string | `"info"` | Log level (`debug`, `info`, `warn`, `error`) |
| `bodyParser.jsonLimit` | string | `"10mb"` | Maximum JSON payload size |

### **Step 3: Verify Configuration**

After configuring both files, your service structure should look like:

```
your-service/
├── server/
│   ├── config.json             # ✅ MCP module enabled
│   ├── config/
│   │   └── mcp.json            # ✅ MCP server configured
│   └── mcp/
│       ├── config.js           # ⏭️ Next: Extension configuration
│       └── mcp-extension.js    # ⏭️ Next: Extension implementation
```

### **Common Configuration Issues**

**❌ MCP module not loading:**
- Check that `modules.mcp.enabled: true` is set in `server/config.json`
- Verify the service has `@perkd/mcp-core` dependency

**❌ Port conflicts:**
- Ensure the MCP port (e.g., 8081) doesn't conflict with the main service port
- Each service should use a unique MCP port

**❌ Authentication failures:**
- Verify OAuth configuration matches your authentication setup
- Check `allowedOrigins` includes your client domains

### **Quick Configuration Templates**

**Minimal Setup (Development):**
```json
// server/config.json (add to modules section)
"mcp": { "enabled": true }

// server/config/mcp.json (create new file)
{
  "enabled": true,
  "name": "dev-yourservice-mcp",
  "version": "1.0.0",
  "port": 8081,
  "security": { "requireAuthentication": false },
  "logging": { "level": "debug" }
}
```

**Production Setup:**
```json
// server/config.json (add to modules section)
"mcp": { "enabled": true }

// server/config/mcp.json (create new file)
{
  "enabled": true,
  "name": "crm-yourservice-mcp",
  "version": "1.0.0",
  "port": 8081,
  "path": "/mcp",
  "transport": "streamable-http",
  "protocolVersion": "2025-06-18",
  "security": {
    "allowedOrigins": ["https://your-domain.com"],
    "requireAuthentication": true,
    "oauth": { "enabled": true }
  },
  "session": {
    "enabled": true,
    "timeout": 1800000,
    "cleanupInterval": 300000
  },
  "logging": {
    "enabled": true,
    "level": "info"
  }
}
```

## �🚀 Quick Start with Factory Libraries

> **Prerequisites:** Complete the [Configuration Setup](#-configuration-setup) section first to enable MCP functionality.

### **Step 1: Create Extension Configuration**

```javascript
// server/mcp/config.js
const { z } = require('zod');

const ServiceConfig = {
  serviceConfig: {
    serviceName: 'MyService',
    defaultLimit: 20,
    maxLimit: 100,
    customOperations: {
      activate: async (modelType, toolPrefix, factory) => {
        // Custom operation implementation
      }
    }
  },
  
  modelConfigs: {
    MyModel: {
      toolPrefix: 'mymodel',
      responseKey: 'item',
      baseSchema: {
        name: z.string().min(1).describe('Item name'),
        active: z.boolean().default(true).describe('Active status')
      },
      specializedTools: [{
        name: 'findByStatus',
        title: 'Find Items by Status',
        description: 'Find items with specific status',
        inputSchema: { status: z.string().describe('Status to filter by') },
        filter: (args) => ({ status: args.status })
      }]
    }
  }
};
```

### **Step 2: Create Extension Implementation**

> **IMPORTANT**: The extension class MUST be named `McpExtension` and file name MUST be `mcp-extension.js`. The CRM MCP module (in /common, shared by all services) is hardcoded to load this class from this file.

> **Prerequisites:** Ensure both `server/config.json` and `server/config/mcp.json` are configured as shown in the [Configuration Setup](#-configuration-setup) section.

```javascript
// server/mcp/mcp-extension.js
const { BaseExtension } = require('@perkd/mcp-core');
const { ServiceConfig } = require('./config');

class McpExtension extends BaseExtension {
  async initialize() {
    // Initialize shared factory libraries
    await this.initializeFactories(ServiceConfig, {
      generateSchema: true,
      generateExamples: true
    });

    // Auto-register all tools
    await this.registerAllFactoryTools();
    
    // Add documentation resources
    await this.registerDocumentationResources();
  }
}

module.exports = { McpExtension };
```

**Result**: Automatically generates 7 tools:
- `mymodel_create`, `mymodel_get`, `mymodel_list`, `mymodel_delete`
- `mymodel_query`, `mymodel_count`
- `mymodel_findByStatus`

## 📋 Configuration Reference

### **Configuration File Relationships**

The MCP system uses a three-tier configuration approach:

1. **Service Configuration** (`server/config.json`) - Enables the MCP module
2. **MCP Server Configuration** (`server/config/mcp.json`) - Configures server settings
3. **Extension Configuration** (`server/mcp/config.js`) - Defines tools and models

```mermaid
graph TB
    A[server/config.json] --> B[MCP Module Enabled]
    C[server/config/mcp.json] --> D[MCP Server Settings]
    E[server/mcp/config.js] --> F[Extension Configuration]

    B --> G[Service Loads MCP Module]
    D --> G
    F --> G

    G --> H[MCP Extension Initialized]
    H --> I[Tools & Resources Available]

    style A fill:#1a237e,color:#ffffff
    style C fill:#4a148c,color:#ffffff
    style E fill:#1b5e20,color:#ffffff
    style I fill:#e65100,color:#ffffff
```

**Configuration Loading Order:**
1. Service loads `config.json` and checks `modules.mcp.enabled`
2. If enabled, MCP module loads `config/mcp.json` for server settings
3. MCP module loads `mcp/config.js` for extension configuration
4. Extension initializes with factory libraries using the configuration

### **ServiceConfig Structure**

```javascript
{
  serviceConfig: {
    serviceName: string,           // Service name for logging
    defaultLimit: number,          // Default pagination limit
    maxLimit: number,             // Maximum pagination limit
    customOperations?: {          // Custom operation handlers
      [operationName]: async (modelType, toolPrefix, factory) => { ... }
    }
  },
  
  modelConfigs: {
    [ModelName]: {
      toolPrefix?: string,         // Tool name prefix (default: lowercase model)
      responseKey?: string,        // Response object key (default: lowercase model)
      idField?: string,           // Primary key field (default: 'id')
      enabledOperations?: string[], // Additional operations to enable
      baseSchema?: ZodRawShape,    // Base Zod schema for all operations
      typeSpecificSchema?: ZodRawShape, // Additional schema for this model
      validationRules?: ValidationRules, // Custom validation rules
      specializedTools?: SpecializedToolConfig[] // Domain-specific tools
    }
  }
}
```

### **Real Example: Image Service**

The Image service demonstrates a complete implementation with 11 models and 91 auto-generated tools:

```javascript
// Simplified Image service configuration
const ImageServiceConfig = {
  serviceConfig: {
    serviceName: 'Image',
    defaultLimit: 20,
    maxLimit: 100,
    customOperations: { clone: ..., upload: ... }
  },
  
  modelConfigs: {
    CardImage: {
      toolPrefix: 'cardimage',
      responseKey: 'image',
      enabledOperations: ['clone', 'upload'],
      baseSchema: {
        name: z.string().optional().describe('Image name'),
        visible: z.boolean().default(true).describe('Visibility')
      },
      typeSpecificSchema: {
        tierLevel: z.number().min(0).max(100).describe('Tier level (0-100)')
      },
      specializedTools: [{
        name: 'findByTierLevel',
        title: 'Find Card Images by Tier Level',
        description: 'Find card images by tier level',
        inputSchema: { tierLevel: z.number().min(0).max(100) },
        filter: (args) => ({ tierLevel: args.tierLevel })
      }]
    },
    
    MessageImage: {
      toolPrefix: 'messageimage',
      responseKey: 'image',
      enabledOperations: ['clone', 'upload'],
      typeSpecificSchema: {
        contentMessageId: z.string().optional().describe('Content message ID')
      },
      specializedTools: [{
        name: 'findByContentMessage',
        title: 'Find Message Images by Content Message',
        description: 'Find images by content message',
        inputSchema: { contentMessageId: z.string() },
        filter: (args) => ({ contentMessageId: args.contentMessageId })
      }]
    }
    // ... 9 more image types
  }
};
```

**Generated Tools**: 91 total across 11 image types
- **44 CRUD operations** (create, get, list, delete)
- **22 query operations** (query, count)
- **3 specialized tools** (findByTierLevel, findByContentMessage, findByName)
- **22 custom operations** (clone, upload)

## 🛠️ Implementation Guide

### **1. Define Models and Operations**

```javascript
modelConfigs: {
  Product: {
    toolPrefix: 'product',
    baseSchema: {
      name: z.string().min(1).describe('Product name'),
      price: z.number().min(0).describe('Product price'),
      active: z.boolean().default(true).describe('Active status')
    },
    specializedTools: [
      {
        name: 'findByCategory',
        title: 'Find Products by Category',
        description: 'Find products in specific category',
        inputSchema: { category: z.string().describe('Product category') },
        filter: (args) => ({ category: args.category }),
        order: 'name ASC'
      }
    ]
  }
}
```

### **2. Add Custom Operations**

```javascript
customOperations: {
  activate: async (modelType, toolPrefix, factory) => {
    factory.extension.server.registerTool(`${toolPrefix}_activate`, {
      title: `Activate ${modelType}`,
      description: `Activate a ${modelType}`,
      inputSchema: {
        [`${modelType.toLowerCase()}Id`]: z.string().describe(`${modelType} ID`)
      }
    }, async (args) => factory.extension.withTenantContext(async () => {
      const { SharedErrorHandler, SharedResponseFormatter, SharedModelHelper } = require('@perkd/mcp-core');
      
      try {
        const Model = SharedModelHelper.getModel(factory.extension.app, modelType);
        const instance = await Model.findById(args[`${modelType.toLowerCase()}Id`]);
        
        if (!instance) {
          return SharedErrorHandler.notFound(modelType, args[`${modelType.toLowerCase()}Id`]);
        }
        
        await instance.updateAttribute('active', true);
        return SharedResponseFormatter.statusChange(
          args[`${modelType.toLowerCase()}Id`], 'activate', true, modelType
        );
      } catch (error) {
        return SharedErrorHandler.handle(error, { operation: 'activate', modelType }, factory.extension);
      }
    }));
  }
}
```

### **3. Register Documentation Resources**

```javascript
async registerDocumentationResources() {
  for (const modelType of Object.keys(ServiceConfig.modelConfigs)) {
    const modelConfig = ServiceConfig.modelConfigs[modelType];
    const toolPrefix = modelConfig.toolPrefix;

    // Schema documentation
    this.registerStaticResource(
      `${toolPrefix}-schema`,
      `${toolPrefix}://schema`,
      { title: `${modelType} Schema`, mimeType: 'application/json' },
      async () => {
        const Model = this.app.models[modelType];
        return SharedDocumentationProvider.generateCompleteDocumentation(Model, modelConfig);
      }
    );
  }
}
```

## 📚 Auto-Generated Documentation

The factory libraries automatically generate comprehensive documentation:

### **Schema Resources**
- `{prefix}://schema` - Complete model schema with property descriptions
- `service://overview` - Service overview with statistics
- `service://types` - Reference guide for all model types

### **Usage Examples**
- `{prefix}://examples` - Usage examples for all tools
- `service://guide` - Interactive tool selection guide

### **Interactive Prompts**
- `service-management-guide` - Interactive tool selection
- `service-upload-assistant` - Step-by-step upload guidance (if applicable)

## 🔧 Advanced Configuration

### **Specialized Tool Patterns**

```javascript
specializedTools: [
  // Find by single field
  {
    name: 'findByStatus',
    inputSchema: { status: z.string() },
    filter: (args) => ({ status: args.status })
  },
  
  // Find by multiple fields
  {
    name: 'findByUserAndStatus',
    inputSchema: { 
      userId: z.string(),
      status: z.enum(['active', 'inactive'])
    },
    filter: (args) => ({ userId: args.userId, status: args.status })
  },
  
  // Find by date range
  {
    name: 'findByDateRange',
    inputSchema: {
      startDate: z.string().describe('Start date (ISO)'),
      endDate: z.string().describe('End date (ISO)')
    },
    filter: (args) => ({
      createdAt: { gte: args.startDate, lte: args.endDate }
    })
  },
  
  // Find single result
  {
    name: 'findByEmail',
    inputSchema: { email: z.string().email() },
    filter: (args) => ({ email: args.email }),
    findOne: true
  }
]
```

### **Validation Rules**

```javascript
validationRules: {
  email: { 
    type: 'string', 
    required: true, 
    format: 'email',
    customValidator: (value) => {
      if (!value.endsWith('@company.com')) {
        return 'Email must be from company domain';
      }
      return null;
    }
  },
  price: { type: 'number', min: 0, required: true },
  name: { type: 'string', required: true, minLength: 1, maxLength: 255 }
}
```

## 🔍 Troubleshooting Configuration

### **MCP Module Not Loading**

**Symptoms:**
- Service starts but no MCP endpoint available
- No MCP-related logs in service output
- `/mcp` endpoint returns 404

**Solutions:**
1. **Check module enablement:**
   ```bash
   # Verify modules.mcp.enabled is true in server/config.json
   grep -A 10 '"modules"' server/config.json
   ```

2. **Verify dependency:**
   ```bash
   # Ensure @perkd/mcp-core is installed
   npm list @perkd/mcp-core
   ```

3. **Check service logs:**
   ```bash
   # Look for MCP initialization messages
   tail -f logs/service.log | grep -i mcp
   ```

### **MCP Server Configuration Issues**

**Symptoms:**
- MCP module loads but server doesn't start
- Port binding errors
- Authentication failures

**Solutions:**
1. **Port conflicts:**
   ```bash
   # Check if MCP port is already in use
   lsof -i :8081
   ```

2. **Configuration validation:**
   ```javascript
   // Validate mcp.json syntax
   node -e "console.log(JSON.parse(require('fs').readFileSync('server/config/mcp.json')))"
   ```

3. **Authentication setup:**
   ```json
   // For development, temporarily disable auth
   {
     "security": {
       "requireAuthentication": false
     }
   }
   ```

### **Extension Loading Failures**

**Symptoms:**
- MCP server starts but no tools available
- Extension initialization errors
- Factory registration failures

**Solutions:**
1. **Check file naming:**
   ```bash
   # Verify exact file names
   ls -la server/mcp/
   # Should show: config.js, mcp-extension.js
   ```

2. **Validate extension configuration:**
   ```javascript
   // Test config.js syntax
   node -e "console.log(require('./server/mcp/config.js'))"
   ```

3. **Check class naming:**
   ```javascript
   // Verify class is named exactly 'McpExtension'
   grep "class McpExtension" server/mcp/mcp-extension.js
   ```

### **Tool Registration Issues**

**Symptoms:**
- Extension loads but tools not available
- Factory initialization errors
- Model access failures

**Solutions:**
1. **Verify model access:**
   ```javascript
   // Check if models are accessible
   console.log(Object.keys(app.models));
   ```

2. **Check factory configuration:**
   ```javascript
   // Validate modelConfigs structure
   const { ImageServiceConfig } = require('./server/mcp/config');
   console.log(Object.keys(ImageServiceConfig.modelConfigs));
   ```

3. **Enable debug logging:**
   ```json
   // In server/config/mcp.json
   {
     "logging": {
       "enabled": true,
       "level": "debug"
     }
   }
   ```

### **Common Configuration Patterns**

**Development Configuration:**
```json
// server/config/mcp.json - Development
{
  "enabled": true,
  "name": "dev-service-mcp",
  "port": 8081,
  "security": {
    "requireAuthentication": false,
    "allowedOrigins": ["*"]
  },
  "logging": {
    "level": "debug"
  }
}
```

**Production Configuration:**
```json
// server/config/mcp.json - Production
{
  "enabled": true,
  "name": "prod-service-mcp",
  "port": 8081,
  "security": {
    "requireAuthentication": true,
    "allowedOrigins": ["https://your-domain.com"],
    "oauth": {
      "enabled": true
    }
  },
  "logging": {
    "level": "info"
  }
}
```
