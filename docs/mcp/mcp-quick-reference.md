# MCP Extensions Quick Reference

> **🚀 MCP Core v3.1.0** - Quick reference for shared factory libraries

## ⚡ Prerequisites

### **1. Enable MCP Module**
```json
// server/config.json (add to modules section)
"modules": {
  "mcp": { "enabled": true }
}
```

### **2. Configure MCP Server**
```json
// server/config/mcp.json (create new file)
{
  "enabled": true,
  "name": "crm-yourservice-mcp",
  "version": "1.0.0",
  "port": 8081,
  "security": { "requireAuthentication": false }, // Dev only
  "logging": { "level": "debug" }
}
```

## 🚀 Quick Start

### **3. Create Extension Configuration**
```javascript
const ServiceConfig = {
  serviceConfig: {
    serviceName: 'MyService',
    defaultLimit: 20,
    maxLimit: 100
  },
  modelConfigs: {
    MyModel: {
      toolPrefix: 'mymodel',
      baseSchema: {
        name: z.string().min(1).describe('Name'),
        active: z.boolean().default(true).describe('Active')
      }
    }
  }
};
```

### **4. Create Extension Implementation**

> **IMPORTANT**: The extension class MUST be named `McpExtension` and file name MUST be `mcp-extension.js`. The CRM MCP module (in /common, shared by all services) is hardcoded to load this class from this file.

```javascript
class McpExtension extends BaseExtension {
  async initialize() {
    await this.initializeFactories(ServiceConfig);
    await this.registerAllFactoryTools();
  }
}
```

## 📋 Configuration Templates

### **Service Configuration Templates**

#### **Development Setup**
```json
// server/config.json (modules section)
"modules": {
  "mcp": { "enabled": true }
}

// server/config/mcp.json
{
  "enabled": true,
  "name": "dev-service-mcp",
  "port": 8081,
  "security": { "requireAuthentication": false },
  "logging": { "level": "debug" }
}
```

#### **Production Setup**
```json
// server/config.json (modules section)
"modules": {
  "mcp": { "enabled": true }
}

// server/config/mcp.json
{
  "enabled": true,
  "name": "crm-service-mcp",
  "version": "1.0.0",
  "port": 8081,
  "transport": "streamable-http",
  "protocolVersion": "2025-06-18",
  "security": {
    "allowedOrigins": ["https://your-domain.com"],
    "requireAuthentication": true,
    "oauth": { "enabled": true }
  },
  "session": {
    "enabled": true,
    "timeout": 1800000,
    "cleanupInterval": 300000
  },
  "logging": {
    "enabled": true,
    "level": "info"
  }
}
```

### **Extension Configuration Templates**

#### **Basic Model**
```javascript
BasicModel: {
  toolPrefix: 'basic',
  baseSchema: {
    name: z.string().min(1).describe('Name'),
    active: z.boolean().default(true).describe('Active status')
  }
}
```

### **Model with Specialized Tools**
```javascript
AdvancedModel: {
  toolPrefix: 'advanced',
  baseSchema: {
    name: z.string().min(1).describe('Name'),
    status: z.enum(['active', 'inactive']).describe('Status')
  },
  specializedTools: [{
    name: 'findByStatus',
    title: 'Find by Status',
    description: 'Find items by status',
    inputSchema: { status: z.string().describe('Status') },
    filter: (args) => ({ status: args.status })
  }]
}
```

### **Model with Custom Operations**
```javascript
CustomModel: {
  toolPrefix: 'custom',
  enabledOperations: ['clone', 'activate'],
  baseSchema: {
    name: z.string().min(1).describe('Name')
  }
}

// In serviceConfig.customOperations:
clone: async (modelType, toolPrefix, factory) => {
  // Custom clone implementation
}
```

## 🔧 Common Patterns

### **Specialized Tool Patterns**

| Pattern | Example |
|---------|---------|
| **Single Field** | `filter: (args) => ({ status: args.status })` |
| **Multiple Fields** | `filter: (args) => ({ userId: args.userId, active: args.active })` |
| **Range Query** | `filter: (args) => ({ price: { gte: args.min, lte: args.max } })` |
| **Date Range** | `filter: (args) => ({ createdAt: { gte: args.start, lte: args.end } })` |
| **Single Result** | `findOne: true` |
| **Custom Order** | `order: 'name ASC, createdAt DESC'` |

### **Validation Patterns**

| Type | Zod Schema | Description |
|------|------------|-------------|
| **Required String** | `z.string().min(1)` | Non-empty string |
| **Optional String** | `z.string().optional()` | Optional string |
| **Email** | `z.string().email()` | Valid email format |
| **Enum** | `z.enum(['A', 'B', 'C'])` | Limited choices |
| **Number Range** | `z.number().min(0).max(100)` | Number within range |
| **Boolean** | `z.boolean().default(true)` | Boolean with default |
| **Array** | `z.array(z.string())` | Array of strings |
| **Object** | `z.object({ key: z.string() })` | Nested object |

### **Error Handling**

```javascript
const { SharedErrorHandler, SharedValidationHelper } = require('@perkd/mcp-core');

// Validation
const error = SharedValidationHelper.validateId(args.id, 'id');
if (error) return error;

// Not found
return SharedErrorHandler.notFound(modelType, id);

// Validation error
return SharedErrorHandler.validationError(field, value, constraint);

// Generic error handling
return SharedErrorHandler.handle(error, context, extension);
```

### **Response Formatting**

```javascript
const { SharedResponseFormatter } = require('@perkd/mcp-core');

// Success response
return SharedResponseFormatter.success(data, responseKey);

// List response
return SharedResponseFormatter.list(items, { total, limit, skip }, metadata);

// Status change
return SharedResponseFormatter.statusChange(id, operation, newStatus, modelType);

// Count response
return SharedResponseFormatter.count(count, query, metadata);
```

## 📊 Generated Tools

For each model, the factory automatically generates:

| Tool Pattern | Description | Example |
|--------------|-------------|---------|
| **`{prefix}_create`** | Create new instance | `product_create` |
| **`{prefix}_get`** | Get by ID | `product_get` |
| **`{prefix}_list`** | List with pagination | `product_list` |
| **`{prefix}_delete`** | Delete by ID | `product_delete` |
| **`{prefix}_query`** | Advanced query | `product_query` |
| **`{prefix}_count`** | Count records | `product_count` |
| **`{prefix}_{specialized}`** | Specialized tools | `product_findByCategory` |
| **`{prefix}_{custom}`** | Custom operations | `product_clone` |

## 📚 Documentation Resources

Auto-generated resources for each model:

| Resource | URI | Description |
|----------|-----|-------------|
| **Schema** | `{prefix}://schema` | Complete model schema |
| **Examples** | `{prefix}://examples` | Usage examples |
| **Upload Guide** | `{prefix}://upload` | Upload documentation (if applicable) |
| **Service Overview** | `service://overview` | Service statistics |
| **Types Reference** | `service://types` | All model types |

## 🎯 Image Service Example

Real-world example from the Image service:

### **Configuration Summary**
- **11 image models** (CardImage, MessageImage, LogoImage, etc.)
- **91 auto-generated tools** (44 CRUD, 22 query, 3 specialized, 22 custom)

### **Key Models**
```javascript
CardImage: {
  toolPrefix: 'cardimage',
  typeSpecificSchema: {
    tierLevel: z.number().min(0).max(100).describe('Tier level')
  },
  specializedTools: [{
    name: 'findByTierLevel',
    inputSchema: { tierLevel: z.number().min(0).max(100) },
    filter: (args) => ({ tierLevel: args.tierLevel })
  }]
},

MessageImage: {
  toolPrefix: 'messageimage',
  typeSpecificSchema: {
    contentMessageId: z.string().optional().describe('Content message ID')
  },
  specializedTools: [{
    name: 'findByContentMessage',
    inputSchema: { contentMessageId: z.string() },
    filter: (args) => ({ contentMessageId: args.contentMessageId })
  }]
}
```

## 🔍 Troubleshooting

### **Configuration Issues**

| Issue | Cause | Solution |
|-------|-------|----------|
| **MCP module not loading** | `modules.mcp.enabled` not set | Add to `server/config.json` |
| **MCP server not starting** | Invalid `mcp.json` | Check JSON syntax |
| **Port conflicts** | Port already in use | Change port in `mcp.json` |
| **Authentication failures** | OAuth misconfiguration | Set `requireAuthentication: false` for dev |

### **Extension Issues**

| Issue | Cause | Solution |
|-------|-------|----------|
| **Tools not registered** | Missing model config | Add to `modelConfigs` |
| **Validation errors** | Invalid Zod schema | Check schema syntax |
| **Tool conflicts** | Duplicate prefixes | Use unique `toolPrefix` |
| **Missing documentation** | Resources not registered | Call `registerDocumentationResources()` |

### **Debug Mode**

```javascript
await this.initializeFactories(config, {
  generateSchema: true,
  generateExamples: true
}, true); // Enable debug logging
```

### **Configuration Validation**

```javascript
// Test configuration
function validateConfig(config) {
  assert(config.serviceConfig.serviceName);
  assert(config.serviceConfig.defaultLimit > 0);
  
  for (const [modelType, modelConfig] of Object.entries(config.modelConfigs)) {
    assert(modelConfig.toolPrefix);
    const schema = z.object(modelConfig.baseSchema);
    // Schema should compile without errors
  }
}
```

---

## ✅ Configuration Checklist

### **Before Starting Development**
- [ ] `modules.mcp.enabled: true` in `server/config.json`
- [ ] `server/config/mcp.json` created with valid JSON
- [ ] Unique MCP port configured (e.g., 8081)
- [ ] Service has `@perkd/mcp-core` dependency

### **File Structure Verification**
```
your-service/
├── server/
│   ├── config.json             # ✅ MCP module enabled
│   ├── config/
│   │   └── mcp.json            # ✅ MCP server configured
│   └── mcp/
│       ├── config.js           # ✅ Extension configuration
│       └── mcp-extension.js    # ✅ Extension implementation
```

### **Quick Test Commands**
```bash
# Verify configuration syntax
node -e "console.log(JSON.parse(require('fs').readFileSync('server/config/mcp.json')))"

# Check if MCP module is enabled
grep -A 5 '"mcp"' server/config.json

# Test extension configuration
node -e "console.log(require('./server/mcp/config.js'))"
```

## 🎯 Next Steps

1. **Complete configuration setup** - Follow checklist above
2. **Start with basic configuration** - Single model, simple schema
3. **Add specialized tools** - Common query patterns
4. **Implement custom operations** - Domain-specific needs
5. **Generate documentation** - Auto-generated resources
6. **Test thoroughly** - Validate all functionality
7. **Migrate gradually** - One model at a time

**🚀 The shared factory libraries transform MCP development from manual coding to configuration-driven patterns that scale across all services.**
