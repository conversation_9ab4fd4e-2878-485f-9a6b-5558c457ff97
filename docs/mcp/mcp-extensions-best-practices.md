# MCP Extensions Best Practices

> **🚀 MCP Core v3.1.0** - Best practices for shared factory libraries

This guide provides best practices for developing MCP extensions using the modern shared factory libraries approach, based on real-world implementation experience from the Image service migration.

## Table of Contents

- [🎯 Core Principles](#-core-principles)
- [🏗️ Configuration Best Practices](#️-configuration-best-practices)
- [🔧 Implementation Patterns](#-implementation-patterns)
- [📚 Documentation Standards](#-documentation-standards)
- [⚡ Performance Guidelines](#-performance-guidelines)
- [🧪 Testing Strategies](#-testing-strategies)

## 🎯 Core Principles

### **1. Configuration-Driven Development**

✅ **DO**: Use shared factory libraries for all new extensions
```javascript
// ✅ Modern approach - Configuration-driven
const config = {
  serviceConfig: { serviceName: 'MyService', defaultLimit: 20, maxLimit: 100 },
  modelConfigs: {
    MyModel: {
      toolPrefix: 'mymodel',
      baseSchema: { name: z.string(), active: z.boolean() },
      specializedTools: [{ name: 'findByStatus', /* ... */ }]
    }
  }
};
```

❌ **DON'T**: Write manual tool registration
```javascript
// ❌ Legacy approach - Manual registration
this.server.registerTool('mymodel_create', { /* ... */ }, async (args) => {
  // Hundreds of lines of repetitive code
});
```

### **2. Leverage Shared Utilities**

✅ **DO**: Use shared utilities for consistency
```javascript
const { SharedErrorHandler, SharedValidationHelper, SharedResponseFormatter } = require('@perkd/mcp-core');

// Consistent error handling
return SharedErrorHandler.notFound(modelType, id);

// Standardized validation
const error = SharedValidationHelper.validateId(args.id, 'id');
if (error) return error;

// Uniform responses
return SharedResponseFormatter.success(result, 'item');
```

❌ **DON'T**: Create service-specific utilities
```javascript
// ❌ Avoid custom utilities that duplicate shared functionality
class CustomErrorHandler { /* ... */ }
```

### **3. Type-Safe Configuration**

✅ **DO**: Use Zod schemas for all inputs
```javascript
baseSchema: {
  name: z.string().min(1).max(255).describe('Item name'),
  price: z.number().min(0).describe('Price in cents'),
  active: z.boolean().default(true).describe('Active status')
}
```

❌ **DON'T**: Skip validation or use weak typing
```javascript
// ❌ Weak validation
baseSchema: {
  name: z.any(), // Too permissive
  price: z.string() // Wrong type
}
```

## 🏗️ Configuration Best Practices

### **Service Configuration**

```javascript
serviceConfig: {
  serviceName: 'MyService',        // Clear, descriptive name
  defaultLimit: 20,               // Reasonable default
  maxLimit: 100,                  // Prevent abuse
  customOperations: {
    // Only add operations that can't be handled by standard patterns
    activate: async (modelType, toolPrefix, factory) => { /* ... */ }
  }
}
```

### **Model Configuration Patterns**

#### **Simple Model**
```javascript
SimpleModel: {
  toolPrefix: 'simple',           // Short, clear prefix
  baseSchema: {
    name: z.string().min(1).describe('Name'),
    active: z.boolean().default(true).describe('Active status')
  }
}
```

#### **Complex Model with Specialized Tools**
```javascript
ComplexModel: {
  toolPrefix: 'complex',
  responseKey: 'item',            // Custom response key if needed
  enabledOperations: ['activate', 'clone'], // Custom operations
  baseSchema: {
    name: z.string().min(1).max(255).describe('Item name'),
    description: z.string().optional().describe('Item description')
  },
  typeSpecificSchema: {
    category: z.enum(['A', 'B', 'C']).describe('Item category'),
    priority: z.number().min(1).max(10).describe('Priority level')
  },
  specializedTools: [
    {
      name: 'findByCategory',
      title: 'Find Items by Category',
      description: 'Find items in specific category',
      inputSchema: {
        category: z.enum(['A', 'B', 'C']).describe('Category to filter by'),
        active: z.boolean().optional().describe('Filter by active status')
      },
      filter: (args) => ({ 
        category: args.category,
        ...(args.active !== undefined && { active: args.active })
      }),
      order: 'name ASC'
    }
  ]
}
```

### **Naming Conventions**

| Element | Convention | Example |
|---------|------------|---------|
| **Service Name** | PascalCase | `Image`, `Product`, `User` |
| **Tool Prefix** | lowercase | `cardimage`, `product`, `user` |
| **Model Names** | PascalCase | `CardImage`, `ProductVariant` |
| **Field Names** | camelCase | `tierLevel`, `contentMessageId` |
| **Tool Names** | prefix_operation | `cardimage_findByTierLevel` |

## 🔧 Implementation Patterns

### **Extension Structure**

```javascript
class McpExtension extends BaseExtension {
  async initialize() {
    // 1. Initialize factories
    await this.initializeFactories(ServiceConfig, {
      generateSchema: true,
      generateExamples: true,
      generateUploadDocs: false // Only if service handles uploads
    });

    // 2. Register all tools
    await this.registerAllFactoryTools();

    // 3. Add documentation
    await this.registerDocumentationResources();

    // 4. Add interactive prompts
    await this.registerServicePrompts();
  }

  // Keep extension code minimal - let factories do the work
}
```

### **Custom Operations Pattern**

```javascript
customOperations: {
  clone: async (modelType, toolPrefix, factory) => {
    factory.extension.server.registerTool(`${toolPrefix}_clone`, {
      title: `Clone ${modelType}`,
      description: `Create a copy of existing ${modelType}`,
      inputSchema: {
        sourceId: z.string().describe(`ID of ${modelType} to clone`),
        newName: z.string().optional().describe('Name for cloned item')
      }
    }, async (args) => factory.extension.withTenantContext(async () => {
      const { SharedErrorHandler, SharedResponseFormatter, SharedModelHelper } = require('@perkd/mcp-core');
      
      try {
        // 1. Validate input
        const idError = SharedValidationHelper.validateId(args.sourceId, 'sourceId');
        if (idError) return idError;

        // 2. Get source
        const Model = SharedModelHelper.getModel(factory.extension.app, modelType);
        const source = await Model.findById(args.sourceId);
        if (!source) {
          return SharedErrorHandler.notFound(modelType, args.sourceId);
        }

        // 3. Create clone
        const cloneData = { ...source.toJSON() };
        delete cloneData.id;
        delete cloneData.createdAt;
        delete cloneData.updatedAt;
        if (args.newName) cloneData.name = args.newName;

        const clone = await Model.create(cloneData);
        return SharedResponseFormatter.success(clone.toJSON(), toolPrefix);
      } catch (error) {
        return SharedErrorHandler.handle(error, {
          operation: 'clone',
          modelType,
          sourceId: args.sourceId
        }, factory.extension);
      }
    }));
  }
}
```

### **Specialized Tools Patterns**

```javascript
// Pattern 1: Simple field filter
{
  name: 'findByStatus',
  inputSchema: { status: z.string() },
  filter: (args) => ({ status: args.status })
}

// Pattern 2: Multiple field filter
{
  name: 'findByUserAndStatus',
  inputSchema: { 
    userId: z.string(),
    status: z.enum(['active', 'inactive'])
  },
  filter: (args) => ({ userId: args.userId, status: args.status })
}

// Pattern 3: Range filter
{
  name: 'findByPriceRange',
  inputSchema: {
    minPrice: z.number().min(0),
    maxPrice: z.number().min(0)
  },
  filter: (args) => ({ price: { gte: args.minPrice, lte: args.maxPrice } })
}

// Pattern 4: Single result
{
  name: 'findByEmail',
  inputSchema: { email: z.string().email() },
  filter: (args) => ({ email: args.email }),
  findOne: true
}
```

## 📚 Documentation Standards

### **Auto-Generated Resources**

Always register these standard resources:

```javascript
async registerDocumentationResources() {
  for (const modelType of Object.keys(ServiceConfig.modelConfigs)) {
    const config = ServiceConfig.modelConfigs[modelType];
    const prefix = config.toolPrefix;

    // Schema documentation
    this.registerStaticResource(`${prefix}-schema`, `${prefix}://schema`, 
      { title: `${modelType} Schema` },
      async () => SharedDocumentationProvider.generateCompleteDocumentation(
        this.app.models[modelType], config
      )
    );

    // Usage examples
    this.registerStaticResource(`${prefix}-examples`, `${prefix}://examples`,
      { title: `${modelType} Examples` },
      async () => {
        const docs = SharedDocumentationProvider.generateCompleteDocumentation(
          this.app.models[modelType], config
        );
        return docs.examples;
      }
    );
  }

  // Service overview
  this.registerStaticResource('service-overview', 'service://overview',
    { title: 'Service Overview' },
    () => this.generateServiceOverview()
  );
}
```

### **Interactive Prompts**

```javascript
async registerServicePrompts() {
  this.server.registerPrompt('service-guide', {
    title: 'Service Management Guide',
    description: 'Interactive guide for using service tools'
  }, async () => ({
    messages: [
      {
        role: 'user',
        content: { type: 'text', text: 'What tools are available?' }
      },
      {
        role: 'assistant',
        content: { 
          type: 'text', 
          text: `Available tools:\n${this.generateToolsList()}` 
        }
      }
    ]
  }));
}
```

## ⚡ Performance Guidelines

### **Configuration Optimization**

✅ **DO**: Keep configurations focused
```javascript
// ✅ Focused configuration
modelConfigs: {
  Product: {
    toolPrefix: 'product',
    baseSchema: { /* only essential fields */ },
    specializedTools: [/* only commonly used patterns */]
  }
}
```

❌ **DON'T**: Over-configure
```javascript
// ❌ Over-configured
modelConfigs: {
  Product: {
    // Too many specialized tools that are rarely used
    specializedTools: [/* 20+ specialized tools */]
  }
}
```

### **Pagination Best Practices**

```javascript
serviceConfig: {
  defaultLimit: 20,    // Reasonable default
  maxLimit: 100        // Prevent abuse
}

// In specialized tools
inputSchema: {
  limit: z.number().min(1).max(100).optional().default(20),
  skip: z.number().min(0).optional().default(0)
}
```

### **Memory Management**

- **Lazy load** documentation resources
- **Cache** frequently accessed configurations
- **Limit** result set sizes with proper pagination

## 🧪 Testing Strategies

### **Configuration Testing**

```javascript
// Test configuration validity
function testConfiguration() {
  const config = ServiceConfig;
  
  // Validate service config
  assert(config.serviceConfig.serviceName);
  assert(config.serviceConfig.defaultLimit > 0);
  assert(config.serviceConfig.maxLimit >= config.serviceConfig.defaultLimit);
  
  // Validate model configs
  for (const [modelType, modelConfig] of Object.entries(config.modelConfigs)) {
    assert(modelConfig.toolPrefix);
    assert(modelConfig.baseSchema);
    
    // Test Zod schemas compile
    const schema = z.object(modelConfig.baseSchema);
    schema.parse({}); // Should not throw for optional fields
  }
}
```

### **Tool Generation Testing**

```javascript
// Test tool generation
function testToolGeneration() {
  const totalTools = calculateExpectedTools(ServiceConfig);
  console.log(`Expected tools: ${totalTools}`);
  
  // Verify each model generates expected tools
  for (const modelType of Object.keys(ServiceConfig.modelConfigs)) {
    const config = ServiceConfig.modelConfigs[modelType];
    const expectedTools = 6 + // CRUD + query
      (config.specializedTools?.length || 0) +
      (config.enabledOperations?.length || 0);
    console.log(`${modelType}: ${expectedTools} tools`);
  }
}
```

---

## 🎯 Quick Reference

### **Essential Imports**
```javascript
const { BaseExtension, SharedDocumentationProvider } = require('@perkd/mcp-core');
const { z } = require('zod');
```

### **Minimal Extension**
```javascript
class McpExtension extends BaseExtension {
  async initialize() {
    await this.initializeFactories(ServiceConfig);
    await this.registerAllFactoryTools();
  }
}
```

### **Standard Configuration**
```javascript
const ServiceConfig = {
  serviceConfig: { serviceName: 'Service', defaultLimit: 20, maxLimit: 100 },
  modelConfigs: {
    Model: {
      toolPrefix: 'model',
      baseSchema: { name: z.string(), active: z.boolean() }
    }
  }
};
```

**🚀 Following these best practices ensures consistent, maintainable, and scalable MCP extensions across all CRM services.**
