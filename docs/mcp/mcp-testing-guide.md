# MCP Testing Guide

This guide provides step-by-step instructions for testing MCP extensions using the Streamable HTTP transport (protocol version 2025-06-18), with specific focus on the **modern factory-based architecture** and comprehensive error handling.

## Quick Reference

### 1. Start the MCP Server

```bash
NODE_ENV=development node --require dotenv/config .
```

### 2. Test Basic Endpoints

```bash
# Test the status endpoint
curl http://localhost:8088/mcp/status
```

### 3. Generate a Valid JWT Token

```bash
# Generate a token for testing with correct audience claim
# CRITICAL: Must include 'aud' (audience) claim matching server URL
TOKEN=$(node --require dotenv/config -e "console.log(require('jsonwebtoken').sign({ tenant: { code: 'TEST' }, aud: 'http://localhost:8088' }, process.env.PERKD_SECRET_KEY || 'test-secret-key'))")

echo "Token: $TOKEN"
```

**Important:** The JWT token must include an `aud` (audience) claim that matches the server URL. Without this, you'll get a 403 Forbidden error with "Token missing audience claim" or "Token not intended for this resource server".

### 4. Verify Tool Schema Format (Critical Check)

Before testing tool functionality, verify that your tools use **Zod schemas** (not JSON Schema):

> **⚠️ CRITICAL:** Your MCP extension must import and use Zod schemas:
> ```javascript
> const { z } = require('zod');
> // or
> import { z } from 'zod';
> ```

```bash
# List tools and check if input fields are properly defined
TOKEN=$(node --require dotenv/config -e "console.log(require('jsonwebtoken').sign({ tenant: { code: 'TEST' }, aud: 'http://localhost:8088' }, process.env.PERKD_SECRET_KEY || 'test-secret-key'))")

# Initialize session
SESSION_RESPONSE=$(curl -i -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"jsonrpc":"2.0","id":"init-1","method":"initialize","params":{"clientInfo":{"name":"test-client","version":"1.0.0"},"supportedProtocolVersions":["2025-06-18"],"capabilities":{},"protocolVersion":"2025-06-18"}}')

# Extract session ID
SESSION_ID=$(echo "$SESSION_RESPONSE" | grep -i "mcp-session-id:" | cut -d' ' -f2 | tr -d '\r')

# List tools and check schema format
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"tools-1","method":"tools/list","params":{}}' | jq '.result.tools[0].inputSchema'
```

**Expected Result:** Should show Zod-style schema properties, not JSON Schema structure.

**⚠️ If you see JSON Schema format** (with `type`, `properties`, `required`), your tools will not display input fields in MCP clients.

### 5. Initialize a Session

```bash
# Initialize a session (required for Streamable HTTP transport)
# Use -i flag to include headers in the response
# CRITICAL: The Accept header must include both application/json and text/event-stream
# CRITICAL: The params object must include clientInfo, supportedProtocolVersions, capabilities, and protocolVersion
SESSION_RESPONSE=$(curl -i -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"jsonrpc":"2.0","id":"init-1","method":"initialize","params":{"clientInfo":{"name":"test-client","version":"1.0.0"},"supportedProtocolVersions":["2025-06-18","2025-03-26","2024-11-05"],"capabilities":{},"protocolVersion":"2025-06-18"}}')

echo "Session response (including headers):"
echo "$SESSION_RESPONSE"

# Extract the session ID from the response headers
# According to MCP spec, the session ID is in the Mcp-Session-Id header
SESSION_ID=$(echo "$SESSION_RESPONSE" | grep -i "mcp-session-id" | awk '{print $2}' | tr -d '\r')

echo "Session ID: $SESSION_ID"

# CRITICAL: You must include this session ID in all subsequent requests using the Mcp-Session-Id header
# If you get a "Missing session ID" error, this is likely the cause

# NOTE: If the server is configured for stateless operation (session.enabled: false),
# no session ID will be returned, and you don't need to include it in subsequent requests.
# However, stateless mode has limitations: no streaming responses, no server-to-client
# notifications, and no resource subscriptions.

# The refactored implementation uses a dedicated SessionManager component that:
# - Manages session lifecycle (creation, validation, cleanup)
# - Handles session timeout (default: 30 minutes)
# - Performs automatic cleanup of stale sessions (default interval: 5 minutes)
# - Validates session ID and tenant code for all requests
# - Stores session context with tenant information in a context object ({ tenant: tenantCode })
```

### 5. List Available Tools

```bash
# List available tools (with session ID for Streamable HTTP transport)
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"2","method":"tools/list","params":{}}'
```

### 6. Execute a Tool

```bash
# Execute a specific tool (with session ID for Streamable HTTP transport)
# CRITICAL: Use "tools/call" method (not "tools/execute")
# Example: Testing the cardimage_create tool
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"3","method":"tools/call","params":{"name":"cardimage_create","arguments":{"name":"Test Card","tierLevel":1}}}'

# Expected response:
# {
#   "jsonrpc": "2.0",
#   "id": "3",
#   "result": {
#     "content": [
#       {
#         "type": "text",
#         "text": "{\"products\":[{\"id\":\"123\",\"name\":\"Product 1\"},{\"id\":\"456\",\"name\":\"Product 2\"}]}"
#       }
#     ]
#   }
# }
```

### 7. Testing Factory-Generated Tools (Modern Architecture)

The refactored MCP extension uses factory classes to generate tools automatically. Here's how to test the different types of tools:

#### **Testing CRUD Tools**

Each image type gets 6 standard CRUD tools automatically generated:

```bash
# Test CREATE tool with validation
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"create-1","method":"tools/call","params":{"name":"cardimage_create","arguments":{"name":"Test Card","visible":true,"tierLevel":1,"index":0}}}'

# Test GET tool
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"get-1","method":"tools/call","params":{"name":"cardimage_get","arguments":{"imageId":"img_123456789"}}}'

# Test LIST tool with pagination
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"list-1","method":"tools/call","params":{"name":"cardimage_list","arguments":{"where":{"visible":true},"order":"createdAt DESC","limit":10,"skip":0}}}'

# Test UPLOAD tool
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"upload-1","method":"tools/call","params":{"name":"cardimage_upload","arguments":{"imageId":"img_123456789","filename":"test.jpg","mimeType":"image/jpeg","file":"base64-encoded-data..."}}}'
```

#### **Testing Specialized Tools**

Test type-specific tools that are configuration-driven:

```bash
# Test CardImage specialized tool
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"spec-1","method":"tools/call","params":{"name":"cardimage_findByTierLevel","arguments":{"tierLevel":1,"limit":10,"skip":0}}}'

# Test MessageImage specialized tool
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"spec-2","method":"tools/call","params":{"name":"messageimage_findByContentMessage","arguments":{"contentMessageId":"msg_123456789","limit":20}}}'
```

#### **Testing Error Handling**

The new architecture provides comprehensive error handling. Test various error scenarios:

```bash
# Test validation errors (empty ID)
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"error-1","method":"tools/call","params":{"name":"cardimage_get","arguments":{"imageId":""}}}'

# Expected error response:
# {
#   "jsonrpc": "2.0",
#   "id": "error-1",
#   "result": {
#     "content": [{
#       "type": "text",
#       "text": "{\"error\":\"VALIDATION_ERROR\",\"message\":\"Validation failed for field 'imageId': must be at least 1 characters long\",\"code\":400,\"field\":\"imageId\",\"value\":\"\",\"constraint\":\"must be at least 1 characters long\",\"timestamp\":\"2024-01-15T10:30:00.000Z\"}"
#     }]
#   }
# }

# Test not found errors
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"error-2","method":"tools/call","params":{"name":"cardimage_get","arguments":{"imageId":"nonexistent"}}}'

# Test upload validation errors (missing file and URL)
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"error-3","method":"tools/call","params":{"name":"cardimage_upload","arguments":{"imageId":"img_123","filename":"test.jpg"}}}'
```

#### **Testing Query Tools**

Test the automatically generated query and count tools:

```bash
# Test query tool with advanced filtering
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"query-1","method":"tools/call","params":{"name":"cardimage_query","arguments":{"where":{"tierLevel":{"gte":1,"lte":5},"visible":true},"order":"index ASC, createdAt DESC","limit":20,"skip":0}}}'

# Test count tool
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"count-1","method":"tools/call","params":{"name":"cardimage_count","arguments":{"where":{"visible":true}}}}'
```

### 8. List Available Resources

```bash
# List available resources (with session ID for Streamable HTTP transport)
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"4","method":"resources/list","params":{}}'
```

### 8. Read a Resource

```bash
# Read a specific resource (with session ID for Streamable HTTP transport)
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"5","method":"resources/read","params":{"uri":"schema://your_model/definition"}}'
```

### 9. Get a Prompt

```bash
# Get a specific prompt (with session ID for Streamable HTTP transport)
curl -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"6","method":"prompts/get","params":{"name":"your_prompt_name","arguments":{"param1":"value1"}}}'
```

### 10. Interactive Testing with mcp-remote

```bash
# For more complex interactions, use mcp-remote (handles session management automatically)
npx -y mcp-remote@latest http://localhost:8088/mcp --transport http-only --allow-http --auth "Bearer $TOKEN"
```

## Troubleshooting Common Issues

### Missing Session ID Error

```
{"jsonrpc":"2.0","error":{"code":-32005,"message":"Missing session ID"},"id":null}
```

- **Cause**: You're trying to make a request without properly initializing a session first, or you're not including the session ID in your request, or using the wrong header name.
- **Solution**:
  - Make sure you've completed step 4 (Initialize a Session)
  - Use the exact header name `Mcp-Session-Id` (note the exact case and format - no 'X-' prefix)
  - Extract the session ID from the response headers, not the response body
  - Verify the session ID is correctly extracted from the initialization response
  - Check that your session hasn't timed out (default is 30 minutes)
  - Include the `Mcp-Session-Id` header in all subsequent requests

**Note**: The refactored implementation uses dedicated utility classes (`ErrorHandler`, `ValidationHelper`, `ResponseFormatter`) to provide consistent error responses with proper error codes and HTTP status codes. All error responses follow a standardized JSON format.

When implementing your own tools and resources, you should use the modern error handling utilities:

```javascript
const { ErrorHandler } = require('./utils/error-handler');
const { ValidationHelper } = require('./utils/validation-helper');
const { ResponseFormatter } = require('./utils/response-formatter');

// Modern error handling pattern
async function myToolHandler(args) {
  try {
    // 1. Validate inputs early
    const validationError = ValidationHelper.validateId(args.userId, 'userId');
    if (validationError) return validationError;

    // 2. Perform operation
    const user = await Model.findById(args.userId);
    if (!user) {
      return ErrorHandler.notFound('User', args.userId, { operation: 'get' });
    }

    // 3. Return success response
    return ResponseFormatter.success(user.toJSON());

  } catch (error) {
    // 4. Handle unexpected errors
    return ErrorHandler.handle(error, {
      operation: 'get',
      userId: args.userId
    }, this);
  }
}

// For resource handlers
async function myResourceHandler(uri) {
  try {
    const resourceId = uri.replace('myresource://', '');
    const resource = await Model.findById(resourceId);

    if (!resource) {
      return ErrorHandler.notFound('Resource', resourceId, { uri });
    }

    return ResponseFormatter.success(resource.toJSON());
  } catch (error) {
    return ErrorHandler.handle(error, { operation: 'read', uri }, this);
  }
}
```

### Session Not Found Error

```
{"jsonrpc":"2.0","error":{"code":-32005,"message":"Session not found"},"id":null}
```

- **Cause**: The session ID you're using is invalid or has expired.
- **Solution**: Initialize a new session and use the new session ID.
- **Technical Details**: The `SessionManager` component validates all session IDs and returns this error if the session ID is not found in its internal session store.

### Authentication Failed Error

```
{"jsonrpc":"2.0","error":{"code":-32001,"message":"Authentication failed"},"id":null}
```

- **Cause**: Your JWT token is invalid or expired.
- **Solution**:
  - Generate a new token using the command in step 3
  - Make sure you're using the correct environment variable for the secret key
  - Verify the token payload contains the tenant code in the correct format: `{ tenant: { code: 'TEST' } }`
  - Check that the token is properly included in the Authorization header as `Bearer <token>`
- **Technical Details**: The authentication middleware validates the JWT token and returns this error if the token is invalid, expired, or doesn't contain the required tenant information.

### Endpoint Not Found Error

```
Cannot POST /mcp/sessions
Cannot GET /mcp/.identity
```

- **Cause**: You're trying to access endpoints that don't exist in the current implementation.
- **Solution**:
  - Make sure you're using the correct endpoint paths as shown in the examples
  - For Streamable HTTP transport, all requests go to `/mcp`
  - The identity endpoint is at `/.identity` (not `/mcp/.identity`)
  - There is no `/mcp/sessions` endpoint in the Streamable HTTP transport

### Initialization Request Issues

```
{"error":"Invalid request format"}
{"error":"Unsupported protocol version"}
```

- **Cause**: The initialization request is missing required fields or has an incorrect format.
- **Solution**:
  - Ensure the initialization request includes all required fields:
    - `clientInfo` with `name` and `version`
    - `supportedProtocolVersions` as an array of supported versions
    - `capabilities` (can be an empty object)
    - `protocolVersion` (optional, but recommended)
  - Make sure the `Accept` header includes both `application/json` and `text/event-stream`
  - Use the `-i` flag with curl to see the full response including headers

### Dynamic Client Registration Failed Error

```
Error: Dynamic client registration failed: HTTP 404
```

- **Cause**: The mcp-remote tool is trying to use a registration endpoint that isn't implemented.
- **Solution**: Make sure you're using the `--transport http-only` flag with mcp-remote.

### JSON-RPC Format Issues

```
{"jsonrpc":"2.0","error":{"code":-32600,"message":"Invalid JSON-RPC request"},"id":null}
{"jsonrpc":"2.0","error":{"code":-32601,"message":"Method not found"},"id":"your-request-id"}
```

- **Cause**: Missing `jsonrpc` field, incorrect method name, or incorrect request format.
- **Solution**:
  - Ensure all requests include `"jsonrpc":"2.0"`
  - Verify the method name is correct (e.g., `tools/call`, not `tools/execute` or `execute`)
  - Make sure the `params` object has the correct structure for the method
  - For tool execution, the tool name should be in the `params.name` field, not in the method name
- **Technical Details**: The `ProtocolHandler` component validates all requests against the JSON-RPC 2.0 specification and returns these errors if the request doesn't conform to the specification.

### Tool Execution Issues

```
{"jsonrpc":"2.0","error":{"code":-32601,"message":"Tool not found: cardimage_list"},"id":"your-request-id"}
{"jsonrpc":"2.0","error":{"code":-32602,"message":"Invalid arguments for tool: cardimage_create"},"id":"your-request-id"}
```

- **Cause**: The tool name is incorrect or the arguments are invalid.
- **Solution**:
  - Verify the tool name exists by listing available tools first
  - Remember that tool names are matched case-insensitively
  - Make sure the arguments match the tool's input schema (must be Zod format)
  - For image service tools, check the specific required parameters (e.g., `tierLevel` for CardImage)
- **Technical Details**: The `ToolRegistry` component validates the tool name and arguments against the registered tools and returns these errors if the tool doesn't exist or the arguments don't match the tool's schema.

## Important Notes

1. The Streamable HTTP transport requires proper session management.
2. All requests must include the `Mcp-Session-Id` header after initialization (note the exact case and format).
3. The session ID is returned in the HTTP response headers, not in the response body.
4. All requests must include the `jsonrpc` field with value `"2.0"`.
5. All requests should include the `Accept` header with both `application/json` and `text/event-stream`.
6. Image service tools use Zod schemas and accept parameters directly in the arguments object.
7. Resource URIs, tool names, and prompt names are matched case-insensitively.

## Refactored Architecture

The MCP implementation has been refactored to use a modular architecture with the following components:

1. **SessionManager**: Centralizes session handling
   - Manages session lifecycle (creation, validation, cleanup)
   - Handles session timeout (default: 30 minutes)
   - Performs automatic cleanup of stale sessions (default interval: 5 minutes)
   - Validates session ID and tenant code for all requests

2. **ConfigurationManager**: Centralizes settings management
   - Validates configuration settings
   - Provides defaults for optional settings
   - Supports dynamic reconfiguration
   - Manages feature flags

3. **ProtocolHandler**: Encapsulates protocol-specific capabilities
   - Handles protocol version negotiation
   - Provides protocol-specific capabilities
   - Supports multiple protocol versions (2025-06-18, 2025-03-26, 2024-11-05)
   - Manages protocol version compatibility

4. **TransportFactory**: Creates appropriate transports
   - Creates Streamable HTTP transports based on configuration
   - Isolates transport-specific code
   - Provides a consistent interface for all transports
   - Handles transport initialization and cleanup

5. **ErrorHandler**: Provides consistent error responses
   - Maps error codes to HTTP status codes
   - Provides helper methods for common error types
   - Ensures consistent error format
   - Improves error logging

6. **DeprecationManager**: Manages deprecated features
   - Provides warnings for deprecated features
   - Helps with migration to newer features
   - Logs deprecation warnings

## HTTP Server Timeout Settings

HTTP server timeout settings are configurable in the MCP configuration:
- `keepAliveTimeout`: Default is 120000ms (2 minutes)
- `headersTimeout`: Default is 125000ms (2 minutes 5 seconds)

For applications with long-running operations, consider increasing the timeout settings:
```javascript
"http": {
  "keepAliveTimeout": 300000,    // 5 minutes
  "headersTimeout": 305000       // 5 minutes 5 seconds
}
```

**Important:** The `headersTimeout` must be greater than the `keepAliveTimeout` to prevent race conditions in the Node.js HTTP server. A good rule of thumb is to set `headersTimeout` to `keepAliveTimeout + 5000` (5 seconds longer).

## Progressive Testing Approach

When implementing MCP extensions, a progressive testing approach with actual server responses provides immediate feedback and simplifies the development process.

### Benefits of Progressive Testing

1. **Immediate Feedback**: Verify endpoint behavior in real-time as you implement each feature
2. **Simplified Debugging**: Issues are easier to identify when testing incrementally
3. **Realistic Testing Environment**: Tests run against the actual runtime environment
4. **Reduced Complexity**: Less need for complex test fixtures and mocks
5. **Documentation By Example**: Test commands become documentation examples

### Progressive Testing Workflow

Follow this workflow for effective progressive testing:

1. **Start with Basic Connectivity** (covered in steps 1-2 above)
2. **Test Authentication** (covered in step 3 above)
3. **Verify Session Management** (covered in steps 4-5 above)
4. **Test Each Tool Individually** (covered in step 6 above)
5. **Test Resource Access** (if applicable)
6. **Test Error Scenarios**
7. **Document Working Examples**

### Advanced Testing with mcp-remote

For interactive testing with automatic session management:

```bash
# Install and use mcp-remote for interactive testing
npx -y mcp-remote@latest http://localhost:8088/mcp --transport http-only --allow-http --auth "Bearer $TOKEN"
```

This tool handles session management automatically and provides an interactive interface for testing tools and resources.

### From Progressive Tests to Automated Tests

Once you have verified your implementation with progressive testing:

1. **Convert curl commands** to automated tests in the `tests/e2e` directory
2. **Ensure test filenames** end with `.test.ts` to be properly detected
3. **Use test utilities** provided in the MCP core package
4. **Run automated tests** with `yarn test:e2e`

Example automated test structure:

```typescript
import { describe, it } from 'node:test';
import assert from 'node:assert';
import { MCPTestClient } from '@perkd/mcp-core/dist/testing';

describe('Image Extension E2E Tests', () => {
  let client: MCPTestClient;

  beforeEach(async () => {
    client = new MCPTestClient('http://localhost:8088/mcp');
    await client.initialize();
  });

  afterEach(async () => {
    await client.disconnect();
  });

  it('should create a card image', async () => {
    const result = await client.callTool('cardimage_create', {
      name: 'Test Card',
      tierLevel: 1
    });

    assert.ok(result.content);
    assert.ok(result.content[0].text.includes('created'));
  });
});
```

### Resource Template Issues

#### Error: "Cannot read properties of undefined (reading 'toString')"

**Symptom**: List Templates fails with this error while List Resources works fine.

**Root Cause**: Incorrect `registerResource` method signature causing static resources to be treated as ResourceTemplates.

**Solution**: Use the correct signature for static resources:

```javascript
// WRONG - causes toString error
this.server.registerResource('myservice://schema', {
  description: 'Schema',
  mimeType: 'application/json'
}, callback)

// CORRECT - static resource registration
this.server.registerResource('schema-resource', 'myservice://schema', {
  description: 'Schema',
  mimeType: 'application/json'
}, callback)
```

**Debugging Steps**:
1. Check server logs for ResourceTemplate creation errors
2. Verify all `registerResource` calls use correct signatures
3. Ensure ResourceTemplate objects have valid `uriTemplate` properties
4. Add validation after ResourceTemplate creation in BaseExtension

## Debugging Techniques

When troubleshooting MCP extension issues:

### 1. Enable Verbose Logging
Set `logging.level` to `"debug"` in your MCP configuration:

```json
{
  "logging": {
    "enabled": true,
    "level": "debug"
  }
}
```

### 2. Check Server Logs
Look for error messages in your server logs, particularly:
- Authentication failures
- Session management issues
- Tool registration errors
- Database connection problems

### 3. Validate JSON Requests
Use a JSON validator to ensure your requests are well-formed:

```bash
# Test JSON validity
echo '{"jsonrpc":"2.0","id":"1","method":"tools/list","params":{}}' | jq .
```

### 4. Test Incrementally
Follow the progressive testing workflow to isolate issues:
- Start with basic connectivity
- Add authentication
- Test session management
- Test individual tools

### 5. Use Network Debugging Tools
Monitor HTTP traffic to understand request/response patterns:

```bash
# Use curl with verbose output
curl -v -X POST http://localhost:8088/mcp \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"jsonrpc":"2.0","id":"1","method":"tools/list","params":{}}'
```

### 6. Check Schema Validation
Verify that your tool schemas are properly formatted:

```bash
# List tools and inspect schema format
curl -X POST http://localhost:8088/mcp \
  -H "Authorization: Bearer $TOKEN" \
  -H "Mcp-Session-Id: $SESSION_ID" \
  -d '{"jsonrpc":"2.0","id":"1","method":"tools/list","params":{}}' | \
  jq '.result.tools[0].inputSchema'
```

If you see JSON Schema format (with `type`, `properties`, `required`), you need to convert to Zod format.

## Unit Testing MCP Extensions

For automated testing of your MCP extensions, you can use the provided test utilities:

```typescript
import { describe, it } from 'node:test';
import assert from 'node:assert';
import { MCPModule, TransportType, ErrorHandler, McpErrorCode } from '@perkd/mcp-core';

describe('My MCP Extension', () => {
  let module: MCPModule;

  beforeEach(() => {
    // Set up a test module
    module = new MCPModule({}, {
      enabled: true,
      name: 'test-mcp',
      version: '1.0.0',
      port: 8080,
      path: '/mcp',
      transport: TransportType.STREAMABLE_HTTP,
      supportedProtocolVersions: ['2025-06-18', '2025-03-26', '2024-11-05']
    });

    // Modern approach: Register extension
    class TestExtension extends BaseExtension {
      async initialize(): Promise<void> {
        this.server.registerTool('my_tool', {
          title: 'Test Tool',
          description: 'Test tool for unit testing',
          inputSchema: {
            param1: z.string().describe('Test parameter')
          }
        }, async (args) => {
          return this.withTenantContext(async () => {
            try {
              return {
                content: [{
                  type: 'text',
                  text: `Processed: ${args.param1}`
                }]
              };
            } catch (error) {
              return {
                content: [{
                  type: 'text',
                  text: `Error: ${error.message}`
                }],
                isError: true
              };
            }
          });
        });
      }
    }

    const extension = new TestExtension(module);
    await extension.initialize();
  });

  it('should register tools correctly', async () => {
    const tools = await module.listTools();
    assert.ok(tools.some(tool => tool.name === 'my_tool'));
  });

  it('should execute tools with proper arguments', async () => {
    const result = await module.executeTool('my_tool', { param1: 'test value' });
    assert.ok(result.content);
    assert.ok(result.content[0].text.includes('Processed: test value'));
  });

  it('should handle errors gracefully', async () => {
    // Test with invalid arguments
    try {
      await module.executeTool('my_tool', {});
      assert.fail('Should have thrown an error');
    } catch (error) {
      assert.ok(error.message.includes('validation'));
    }
  });
});
```

For more comprehensive testing examples, refer to the test files in the `tests/` directory.

## Conclusion

This testing guide provides a comprehensive approach to testing MCP implementations with the Streamable HTTP transport. The key points to remember are:

1. **Session Management**: Always initialize a session and include the session ID in subsequent requests
2. **Authentication**: Use proper JWT tokens with tenant information and correct audience claims
3. **Protocol Compliance**: Follow the JSON-RPC 2.0 specification for all requests
4. **Schema Format**: Ensure all tools use Zod schemas for proper input field rendering
5. **Progressive Testing**: Test each component incrementally for easier debugging
6. **Error Handling**: Understand common error patterns and how to resolve them
