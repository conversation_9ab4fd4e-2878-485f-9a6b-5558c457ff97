# CRM Platform Module System Documentation

## Table of Contents

1. [Module Architecture Overview](#module-architecture-overview)
2. [Event System Deep Dive](#event-system-deep-dive)
3. [Module Lifecycle](#module-lifecycle)
4. [Modules](#modules)
5. [Configuration System](#configuration-system)
6. [Event Flow Diagrams](#event-flow-diagrams)
7. [Troubleshooting Guide](#troubleshooting-guide)
8. [Related Documentation](#related-documentation)

## Module Architecture Overview

The CRM Platform uses a sophisticated module system built on top of LoopBack 3, designed for multi-tenant, event-driven microservices. The architecture provides a standardized way to load, initialize, and manage modules with automatic event subscription and lifecycle management.

### Core Components

1. **Module Loader** (`server/lib/common/server.js`) - Central orchestrator
2. **Module Classes** (`server/lib/common/modules/`) - Individual module implementations
3. **Configuration System** (`server/config/`) - JSON-based module configuration
4. **Event Registry** - External event definitions and patterns
5. **EventBus Module** - Inter-service communication hub

### Module Loading Process

The module system follows a structured loading process:

```javascript
// From server/lib/common/server.js
async function initializeModules(moduleNames, modules, svcName, appConfig) {
    // Initialize common modules
    for (const name of moduleNames) {
        const settings = modules[name]

        if (typeof settings?.enabled === 'boolean') {
            debug('load module [%s], settings: %j', name, settings)
            const Module = appRequire('lib/common/modules/' + name)

            app.Service[name] = new Module(app, settings)

            if (typeof app.Service[name].init === 'function') {
                await app.Service[name].init()
            }

            app.Event = merge(app.Event, Module.Event || {})

            appEcho('[%s] initialized %s', name, settings.enabled ? '' : '(disabled)')
        }
    }

    // Initialize main service module
    const Main = appRequire('lib/' + svcName.toLowerCase())
    app.Service.main = new Main(app, app.service)
    app.Event = merge(app.Event, Main.Event || {})
}
```

### Key Design Principles

- **Convention over Configuration**: Modules follow standardized patterns
- **Event-Driven Architecture**: Modules communicate via events
- **Multi-Tenancy**: Automatic tenant-aware event subscription
- **Lifecycle Management**: Standardized init → ready → start → terminate phases
- **Dependency Injection**: App instance and configuration passed to modules

## Event System Deep Dive

The event system is the backbone of the CRM Platform's module architecture, enabling loose coupling and scalable inter-module communication.

### Event Registry Architecture

The platform uses external event registries (e.g., `@perkd/event-registry-crm`) that define available events:

```javascript
// Event registry structure
{
  "business": {
    "order": {
      "created": "business.order.created",
      "updated": "business.order.updated",
      "paid": "business.order.paid"
    }
  }
}
```

### EventNames() Function Patterns

The system supports two distinct patterns for modules to declare their event interests:

#### Pattern 1: EventEmitter Built-in `eventNames()`

Used by modules that extend `EventEmitter` and register listeners dynamically:

```javascript
// Activity Module example
class ActivityModule extends EventEmitter {
    async add(name) {
        const definition = this.definitions[name]
        let event = definition?.event

        if (event) {
            if (!Array.isArray(event)) event = [event]
            for (const evt of event) {
                this.on(evt, handleEvent) // Registers with EventEmitter
            }
        }
    }
}
// eventNames() automatically returns registered events
```

#### Pattern 2: Custom `eventNames()` Implementation

Used by modules that need to declare events without registering listeners:

```javascript
// Hypothetical custom implementation
class CustomModule {
    eventNames() {
        return [
            'business.order.created',
            'business.payment.processed',
            'business.customer.updated'
        ]
    }
}
```

### Event Subscription Mechanism

The server automatically subscribes modules to events during the ready phase using batch subscription for improved performance:

```javascript
// From handleServiceReady function
if (typeof module.eventNames === 'function') {
    const tenants = app.allTenantCodes(),
          events = module.eventNames(),
          emitter = module

    // Use batchSubscribe with improved error handling
    if (events.length > 0) {
        const subscribePromises = tenants.map(tenant =>
            app.Service.eventbus.batchSubscribe(events, tenant, emitter)
        )
        await Promise.all(subscribePromises)
    }
}
```

### Multi-Tenant Event Handling

The system provides automatic multi-tenant event management:

```javascript
// Tenant addition handling
app.on(MESSAGE.tenant.ADDED, async ({ code }) => {
    for (const name of moduleNames) {
        const settings = modules[name],
              module = app.Service[name]

        if (settings.enabled) {
            if (name === 'eventbus') {
                await module.addTenant(code)
            }
            else if (typeof module.eventNames === 'function') {
                const events = module.eventNames(),
                      emitter = module

                // Use batchSubscribe with improved error handling
                if (events.length > 0) {
                    try {
                        await app.Service.eventbus.batchSubscribe(events, code, emitter)
                    }
                    catch (err) {
                        appNotify('Event subscription failed for new tenant', {
                            module: name,
                            tenant: code,
                            events: events.length,
                            error: err.message
                        }, 'warn')
                    }
                }
            }
        }
    }
})
```

### Event Flow Architecture

1. **External Event** → EventBus receives from Redis/external systems
2. **EventBus** → Routes to subscribed modules based on tenant + event name
3. **Module Handler** → Processes event and may emit new events
4. **Database/Actions** → Module performs business logic and persistence

---

## Module Lifecycle

The CRM Platform defines a standardized lifecycle for all modules, ensuring consistent initialization and cleanup.

### Lifecycle Phases

#### 1. Construction Phase
- Module class instantiated with `app` and `settings`
- Basic properties and dependencies set up
- No async operations or external connections

#### 2. Init Phase (Optional)
```javascript
async init() {
    // Async initialization
    // Database connections, external service setup
    // Event registry loading
}
```

#### 3. Ready Phase
```javascript
ready() {
    // Event listener registration
    // Preset configuration loading
    // Module-specific setup completion
}
```

#### 4. Start Phase (Optional)
```javascript
async start() {
    // Begin active operations
    // Start background processes
    // Enable external interfaces
}
```

#### 5. Terminate Phase
```javascript
async terminate() {
    // Graceful shutdown
    // Close connections
    // Clean up resources
}
```

### Lifecycle Orchestration

The server orchestrates the lifecycle across all modules:

```javascript
// Initialization sequence
await initializeModules(moduleNames, modules, svcName, appConfig)

// Set up service lifecycle handlers
app.on(Event.service.UP, e => {
    app.start(e.service).then(async () => {
        await handleServiceDependencies(e.service)
    })
})

// Ready phase with event subscription
app.on(Event.service.READY, async e => {
    await handleServiceReady(e, moduleNames, modules, appConfig, svcName)
})

// Start phase
app.on(Event.service.STARTED, async () => {
    for (const name in modules) {
        if (modules[name].enabled) {
            if (typeof app.Service[name].start === 'function') {
                await app.Service[name].start()
            }
        }
    }
})
```

---

## Modules

### EventBus Module

**Purpose**: Central event routing and Redis-based inter-service communication

**Event Handling**: Does not implement `eventNames()` - it IS the event system

**Key Features**:
- Redis pub/sub for distributed events
- Tenant-aware event routing
- Event pattern matching and filtering
- Rate limiting and concurrency control

**Configuration** (`eventbus.json`):
```json
{
  "definitions": "@perkd/event-registry-crm",
  "tenant": {
    "subscribe": [
      "place.place.closed",
      "payment.transaction.paid",
      "payment.transaction.authorized",
      "payment.transaction.chargeable",
      "payment.transaction.cancelled",
      "payment.transaction.failed",
      "payment.transaction.event",
      "sales.fulfillment.requested.kitchen",
      "sales.fulfillment.packed.kitchen",
      "sales.fulfillment.allocated.deliver",
      "sales.fulfillment.arrived.deliver",
      "sales.order.created.manual",
      "shopify.shop.redact",
      "applet.service.request"
    ],
    "publish": [
      "business.*",
      "watchdog.*",
      "payment.balance.changed",
      "place.provision.started",
      "place.provision.activated",
      "applet.service.accepted"
    ]
  },
  "mapping": [
    {
      "from": "payment.transaction.paid",
      "to": "payment.transaction"
    },
    {
      "from": "applet.service.request",
      "to": "applet.service"
    }
  ],
  "MAXLEN": 1000,
  "LIMIT": 100
}
```

**Dependencies**:
- `@perkd/eventbus` - Core event bus functionality
- Redis - Message broker
- Event registry package

### Activity Module

**Purpose**: Event-driven activity tracking and logging

**Event Handling**: Uses EventEmitter built-in `eventNames()` after dynamic registration

**Key Features**:
- Activity definitions from external registry
- Event-based activity creation
- Activity merging and caching
- Multi-model activity support

**Configuration** (`activity.json`):
```json
{
  "definitions": "@perkd/activity-registry-crm",
  "options": {
    "maxListeners": 0
  },
  "presets": [
    "auth-login",
    "auth-logout",
    "staff-checkin",
    "staff-checkout",
    "fulfill-item",
    "order-markpaid",
    "order-relocate",
    "offer-issue-staff"
  ]
}
```

**Event Registration Process**:
1. Load activity definitions from registry
2. For each preset, call `add(name)`
3. `add()` extracts events from definition and calls `this.on(event, handler)`
4. EventEmitter tracks registered events
5. `eventNames()` returns tracked events

### Behavior Module

**Purpose**: Behavioral pattern tracking and analysis

**Event Handling**: Uses EventEmitter built-in `eventNames()` with preset-based registration

**Key Features**:
- Behavior definitions from `@perkd/behaviors`
- Model-specific behavior tracking
- Event-driven behavior updates
- Rollback event support

**Configuration** (`behavior.json`):
```json
{
  "presets": {
    "Order": {
      "loyalty": { "foreignKey": "customerId" },
      "frequency": { "foreignKey": "customerId" }
    }
  }
}
```

### Metrics Module

**Purpose**: Prometheus metrics collection and WebSocket streaming

**Event Handling**: No `eventNames()` implementation - uses direct metric sending

**Key Features**:
- Prometheus integration
- WebSocket metrics streaming
- Heartbeat monitoring
- Multi-tenant metric tagging

**Configuration** (`metrics.json`):
```json
{
  "enabled": true,
  "prefix": "crm_business",
  "push": true,
  "http": true,
  "heartbeat": {
    "interval": 30000
  },
  "credentials": {
    "host": "prometheus-gateway",
    "port": 9091
  },
  "options": {
    "timeout": 5000
  }
}
```

### Watchdog Module

**Purpose**: Logging and monitoring with Slack notifications

**Event Handling**: No event subscription - provides logging and notification services

**Key Features**:
- CloudWatch logging integration
- Slack notification system with customizable channels
- Error tracking and stack trace capture
- Multi-tenant context awareness
- Configurable log levels and notification types

**Configuration** (in service config):
```json
{
  "watchdog": {
    "enabled": true,
    "deployment": "production",
    "defaultChannel": "#alerts",
    "logLevel": "info"
  }
}
```

**Key Methods**:
- `init(settings)` - Initializes watchdog with configuration
- `log(message, details, level, logId)` - Logs to CloudWatch
- `notify(message, details, type, channel, stack)` - Sends Slack notifications
- Automatic tenant context injection for multi-tenant environments

**Dependencies**:
- CloudWatch for logging
- Slack integration for notifications
- `@perkd/multitenant-context` for tenant awareness

### Timer Module

**Purpose**: Scheduled task execution and trigger management

**Event Handling**: No event subscription - manages timer triggers

**Key Features**:
- Sets up timer triggers for scheduled tasks
- Multi-tenant timer support with tenant-specific exclusions
- Automatic callback URL generation for model methods
- Integration with Trigger model for persistence
- Configurable delays and repeat intervals

**Configuration** (in service config):
```json
{
  "timer": {
    "enabled": true,
    "delay": 5000,
    "timers": [
      {
        "processor": {
          "model": "Order",
          "method": "processExpired"
        },
        "trigger": {
          "repeatInterval": "0 0 * * *"
        },
        "tenants": ["tenant1", "tenant2"],
        "excludeTenants": ["test-tenant"]
      }
    ]
  }
}
```

**Key Methods**:
- `async start()` - Sets up timer triggers for all configured tenants
- `async setUpTrigger(tenant, timer)` - Creates individual timer trigger
- Automatically injects `{method}_timerCallback` methods into target models

**Dependencies**:
- Trigger model for timer persistence
- Target models for callback execution

### I18n Module

**Purpose**: Internationalization and localization support

**Event Handling**: No event subscription - provides translation services

**Key Features**:
- Multi-language support with configurable namespaces
- Language fallback chains for missing translations
- Integration with i18next library
- Support for multiple file formats and directory structures
- Dynamic locale switching and translation loading

**Configuration** (`i18n` in service config):
```json
{
  "i18n": {
    "enabled": true,
    "namespaces": ["staff", "customer"],
    "filepath": "../../lib/crm/i18n/",
    "languages": ["en", "zh-Hans", "zh-Hant", "ko", "ja"],
    "fallbacks": {
      "zh-Hant-HK": ["zh-Hant-HK", "zh-Hant", "en"],
      "default": ["en"]
    }
  }
}
```

**Key Methods**:
- `init()` - Initializes i18n SDK with configured settings
- Provides translation functions through i18nLib integration

**Dependencies**:
- `i18next` - Core internationalization library
- Custom i18nLib wrapper for Loopback integration

### Provider Module

**Purpose**: External service integration and API management

**Event Handling**: May use custom `eventNames()` for API events

**Key Features**:
- Third-party API integration
- Service provider abstraction
- Rate limiting and retry logic

### Sync Module

**Purpose**: In-memory cache for resource-type objects

**Event Handling**: No event subscription - provides caching functionality

**Key Features**:
- Preloads objects into in-memory cache for fast access
- Supports multiple model types (CardmasterPub, Place, PlaceList, AppSettings)
- Configurable cache limits and TTL
- Automatic data loading from database on startup

**Configuration** (in service config):
```json
{
  "sync": {
    "enabled": true,
    "models": [
      {
        "name": "CardmasterPub",
        "cached": true
      },
      {
        "name": "Place",
        "cached": true
      }
    ]
  }
}
```

**Key Methods**:
- `async init()` - Initializes sync instances and loads cached data
- `async terminate()` - Ends all sync instances
- `get(modelName)` - Retrieves sync instance for specific model
- `settings(modelName)` - Gets configuration for specific model

**Dependencies**:
- `@perkd/sync` - Core synchronization functionality

### MCP Module

**Purpose**: Model Context Protocol server for AI tool integration

**Event Handling**: No event subscription - provides HTTP/WebSocket API endpoints

**Key Features**:
- Model Context Protocol (MCP) server implementation
- Dynamic tool and resource generation for Loopback models
- RESTful API integration for AI assistants
- Streamable HTTP transport with WebSocket support
- OAuth authentication and session management

**Configuration** (`mcp.json`):
```json
{
  "enabled": true,
  "name": "crm-business-mcp",
  "port": 8081,
  "path": "/mcp",
  "transport": "streamable-http",
  "security": {
    "allowedOrigins": ["*"],
    "requireAuthentication": true,
    "oauth": { "enabled": true }
  },
  "session": {
    "enabled": true,
    "timeout": 1800000
  }
}
```

**Dependencies**:
- `@perkd/mcp-core` - Core MCP functionality
- Service-specific MCP extensions in `/mcp/` directory

### Perkd Module

**Purpose**: Perkd-specific integrations and utilities

**Event Handling**: Custom implementation based on requirements

**Key Features**:
- Perkd platform integration
- Custom business logic
- Platform-specific utilities

### OMetrics Module. **DEPRECATED**

**Purpose**: Advanced metrics with aggregation and time series analysis

**Event Handling**: Uses EventEmitter built-in `eventNames()` with dynamic metric registration

**Key Features**:
- Aggregate metrics with multiple accumulator types (sum, count, avg, min, max)
- Time series metrics with configurable scales and intervals
- MongoDB storage for persistent metrics data
- Real-time metric streaming via change streams
- Multi-dimensional metric support with filtering
- TTL-based metric expiration

**Configuration** (in service config):
```json
{
  "ometrics": {
    "enabled": true,
    "aggregates": {
      "ttl": 3600,
      "collection": "metrics_aggregates"
    },
    "timeseries": {
      "ttl": 86400,
      "collection": "metrics_timeseries"
    }
  }
}
```

**Event Registration**:
- Metrics are added dynamically based on definitions from external registry
- Each metric can listen to specific events with custom filters
- `eventNames()` returns all events being monitored across all metrics
- Supports both aggregate and timeseries metric types

**Key Methods**:
- `init()` - Creates dynamic Metrics model with API endpoints
- `add(name, metric)` - Adds new metric with event listeners
- `getAggregateValues(filter)` - Retrieves aggregate metric data
- `getTimeseriesValues(filter)` - Retrieves time series metric data
- `createChangeStream()` - Sets up real-time metric streaming

**Dependencies**:
- MongoDB for metric storage
- External metric definitions registry
- Loopback model system for API generation

---

## Configuration System

The CRM Platform uses a JSON-based configuration system located in `server/config/`.

### Configuration Loading Process

```javascript
function getAppConfig() {
    const { loadAppConfig } = appRequire('lib/common/config-loader')
    const config = merge(loadAppConfig(__dirname), loadAppConfig(appPath()))

    // Load module settings
    if (modules) {
        for (const name in modules) {
            modules[name] = merge(modules[name], appSettings(name))
        }
    }

    return config
}
```

### Module Configuration Structure

Modules are configured in two ways:

1. **Service-level configuration** in `server/config.json`:
```json
{
  "modules": {
    "metrics": { "enabled": true },
    "eventbus": { "enabled": true },
    "activity": { "enabled": true },
    "perkd": { "enabled": true },
    "watchdog": { "enabled": true },
    "provider": { "enabled": true },
    "i18n": {
      "enabled": true,
      "namespaces": ["staff"],
      "filepath": "../../lib/crm/i18n/"
    },
    "mcp": { "enabled": true }
  }
}
```

2. **Module-specific configuration files** in `server/config/`:
```json
{
  "enabled": true,
  "definitions": "@package/registry-name",
  "options": {
    "maxListeners": 0,
    "timeout": 30000
  },
  "presets": ["preset1", "preset2"],
  "credentials": {
    "host": "localhost",
    "port": 6379
  }
}
```

### Configuration Inheritance

1. **Base Configuration**: Default module settings
2. **Environment Overrides**: Environment-specific settings
3. **Runtime Settings**: Dynamic configuration updates

### Common Configuration Patterns

- **enabled**: Boolean flag to enable/disable module
- **definitions**: External package for definitions/registry
- **options**: Module-specific options
- **presets**: Pre-configured setups to initialize
- **credentials**: External service credentials

### Environment-Specific Configuration

```javascript
// Apply environment-specific overrides
const env = process.env.NODE_ENV || 'development'
const envConfig = appSettings(`${env}-config`)

if (envConfig) {
    merge(config, envConfig)
}
```

## Event Flow Diagrams

### Module Initialization Sequence

```mermaid
sequenceDiagram
    participant Server as Server.js
    participant Config as Config Loader
    participant Module as Module Instance
    participant EventBus as EventBus Module
    participant Registry as Event Registry

    Server->>Config: Load app configuration
    Config->>Server: Return merged config

    loop For each enabled module
        Server->>Module: new Module(app, settings)
        Module->>Registry: Load definitions (if applicable)
        Registry->>Module: Return event definitions

        alt Has init() method
            Server->>Module: await init()
            Module->>Module: Setup async resources
        end

        Server->>Module: Merge Module.Event into app.Event
    end

    Server->>Server: Emit service.READY event

    loop For each module with eventNames()
        Server->>Module: module.eventNames()
        Module->>Server: Return event array

        loop For each tenant
            Server->>EventBus: batchSubscribe(events, tenant, module)
            EventBus->>EventBus: Register all subscriptions
        end
    end

    Server->>Server: Emit service.STARTED event

    loop For each enabled module
        alt Has start() method
            Server->>Module: await start()
            Module->>Module: Begin active operations
        end
    end
```

### Multi-Tenant Event Subscription Flow

```mermaid
flowchart TD
    A[Service Ready Event] --> B[Start module processing]
    B --> C{More modules?}
    C -->|Yes| D[Get next module]
    D --> E{Has eventNames?}
    E -->|No| C
    E -->|Yes| F[Call module.eventNames]
    F --> G[Get event array]
    G --> H[Start tenant processing]
    H --> I{More tenants?}
    I -->|Yes| J[Get next tenant]
    J --> K[Start event processing]
    K --> L{More events?}
    L -->|Yes| M[Get next event]
    M --> N[EventBus.batchSubscribe]
    N --> O[Register in subEventsListeners]
    O --> P[Redis PSUBSCRIBE if first listener]
    P --> L
    L -->|No| I
    I -->|No| C
    C -->|No| Q[Subscription Complete]

    R[New Tenant Added] --> S[Start module processing]
    S --> T{More modules?}
    T -->|Yes| U[Get next module]
    U --> V{Has eventNames?}
    V -->|No| T
    V -->|Yes| W[Get events from module.eventNames]
    W --> X[EventBus.batchSubscribe for new tenant]
    X --> T
    T -->|No| Y[New tenant subscribed]
```

### Event Processing Flow

```mermaid
flowchart LR
    A[External System] --> B[Redis Pub/Sub]
    B --> C[EventBus Module]
    C --> D{Event matches subscription?}
    D -->|No| E[Discard]
    D -->|Yes| F[Get tenant listeners]
    F --> G{Has listeners for tenant?}
    G -->|No| E
    G -->|Yes| H[Context.runAsTenant]
    H --> I{Listener type?}
    I -->|Function| J[Call handler function]
    I -->|EventEmitter| K[Call handler.emit]
    I -->|Default| L[app.emit]
    J --> M[Module processes event]
    K --> M
    L --> M
    M --> N[Module may emit new events]
    N --> O[Database operations]
    N --> P[Business logic execution]
```

### Activity Module Event Registration

```mermaid
flowchart TD
    A[Activity Module Constructor] --> B[Load definitions from registry]
    B --> C[Store in this.definitions]
    C --> D[ready called]
    D --> E[Start preset processing]
    E --> F{More presets?}
    F -->|Yes| G[Get next preset]
    G --> H[Call add presetName]
    H --> I[Get definition for preset]
    I --> J{Definition has event?}
    J -->|No| F
    J -->|Yes| K[Extract events]
    K --> L{Event is array?}
    L -->|No| M[Convert to array]
    L -->|Yes| N[Start event processing]
    M --> N
    N --> O{More events?}
    O -->|Yes| P[Get next event]
    P --> Q[this.on event handleEvent]
    Q --> R[EventEmitter registers listener]
    R --> O
    O -->|No| F
    F -->|No| S[eventNames now returns registered events]
```

## Troubleshooting Guide

### Common Timing Issues

#### Symptom: `eventNames()` returns empty array
**Cause**: Module's `ready()` method not completing before `eventNames()` is called
**Solution**: Ensure `ready()` method is properly awaited in server orchestration

#### Symptom: Events not being received by modules
**Cause**: Event listeners not registered before subscription
**Solution**: Verify event registration happens in `ready()` method, not constructor

#### Symptom: Memory leaks during service restarts
**Cause**: Event subscriptions not properly cleaned up
**Solution**: Implement proper unsubscription in module `terminate()` methods

### Debugging Event Subscriptions

```javascript
// Check subscription status
const status = app.Service.eventbus.getSubscribeStatus()
console.log('Current subscriptions:', status)

// Verify module event registration
const events = module.eventNames()
console.log(`${moduleName} registered events:`, events)
```

## Related Documentation

For detailed development information, see:
- **[Module Development Guide](./module-development.md)** - Comprehensive guide for creating and maintaining modules
